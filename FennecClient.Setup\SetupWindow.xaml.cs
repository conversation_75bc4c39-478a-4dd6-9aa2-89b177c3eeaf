using Microsoft.Win32;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;

namespace FennecClient.Setup
{
    public partial class SetupWindow : Window
    {
        public SetupWindow()
        {
            InitializeComponent();
            InstallPathTextBox.Text = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "FennecClient");
        }

        private void Browse_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                ValidateNames = false,
                CheckFileExists = false,
                CheckPathExists = true,
                FileName = "Folder Selection"
            };

            if (dialog.ShowDialog() == true)
            {
                InstallPathTextBox.Text = Path.GetDirectoryName(dialog.FileName);
            }
        }

        private async void Install_Click(object sender, RoutedEventArgs e)
        {
            InstallButton.IsEnabled = false;
            InstallProgress.Visibility = Visibility.Visible;
            StatusText.Visibility = Visibility.Visible;

            try
            {
                StatusText.Text = "Creating installation directory...";
                InstallProgress.Value = 25;

                Directory.CreateDirectory(InstallPathTextBox.Text);

                StatusText.Text = "Installing application files...";
                InstallProgress.Value = 50;

                // Simulate file copying
                await Task.Delay(1000);

                StatusText.Text = "Creating shortcuts...";
                InstallProgress.Value = 75;

                if (CreateDesktopShortcut.IsChecked == true)
                {
                    // Create desktop shortcut logic
                }

                if (CreateStartMenuShortcut.IsChecked == true)
                {
                    // Create start menu shortcut logic
                }

                StatusText.Text = "Installation completed successfully!";
                InstallProgress.Value = 100;

                await Task.Delay(1000);

                MessageBox.Show("Fennec Client has been installed successfully!", "Installation Complete", MessageBoxButton.OK, MessageBoxImage.Information);
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Installation failed: {ex.Message}", "Installation Error", MessageBoxButton.OK, MessageBoxImage.Error);
                InstallButton.IsEnabled = true;
                InstallProgress.Visibility = Visibility.Collapsed;
                StatusText.Visibility = Visibility.Collapsed;
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
} 