using System.Windows.Controls;
using System.Windows;

namespace FennecClient.UI.Controls
{
    public partial class CrosshairWidget : UserControl
    {
        private OverlayWindow _overlayWindow;

        public CrosshairWidget()
        {
            InitializeComponent();
            InitializeOverlay();
            SetupEventHandlers();
        }

        private void InitializeOverlay()
        {
            _overlayWindow = new OverlayWindow();
            // Overlay is always active
            _overlayWindow.ShowOverlay();
            // Initialize crosshair
            UpdateCrosshair();
        }

        private void SetupEventHandlers()
        {
            // Crosshair Toggle
            CrosshairToggle.Checked += (s, e) => UpdateCrosshair();
            CrosshairToggle.Unchecked += (s, e) => UpdateCrosshair();
            
            // Crosshair Sliders
            CrosshairSizeSlider.ValueChanged += (s, e) => UpdateCrosshair();
            CrosshairThicknessSlider.ValueChanged += (s, e) => UpdateCrosshair();
            CrosshairOpacitySlider.ValueChanged += (s, e) => UpdateCrosshair();
        }

        private void UpdateCrosshair()
        {
            bool isVisible = CrosshairToggle?.IsChecked == true;
            double size = CrosshairSizeSlider?.Value ?? 10;
            double thickness = CrosshairThicknessSlider?.Value ?? 2;
            double opacity = CrosshairOpacitySlider?.Value ?? 80;

            _overlayWindow?.UpdateCrosshair(isVisible, size, thickness, opacity);
        }




    }
} 