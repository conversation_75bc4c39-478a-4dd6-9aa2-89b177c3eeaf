#pragma once

#include "imgui.h"

// Forward declarations for embedded font data - note the exact match with the data files
extern const unsigned char g_font_hitmarker_medium[];
extern const unsigned int g_font_hitmarker_medium_size;

extern const unsigned char g_font_hitmarker_bold[];
extern const unsigned int g_font_hitmarker_bold_size;

// Function to load fonts from memory
inline ImFont* LoadFontFromMemory(ImFontAtlas* atlas, const unsigned char* font_data, unsigned int font_size, float size_pixels, const ImFontConfig* font_cfg = NULL)
{
    // Make a copy of the config to avoid modifying the original
    ImFontConfig config;
    if (font_cfg) 
        config = *font_cfg;
    else 
    {
        config.SizePixels = size_pixels;
        config.OversampleH = 2;
        config.OversampleV = 1;
        config.PixelSnapH = true;
    }

    // Add the font from memory
    return atlas->AddFontFromMemoryTTF((void*)font_data, font_size, size_pixels, &config);
}

// Helper function to load the medium font (main font)
inline ImFont* LoadHitmarkerMediumFont(ImFontAtlas* atlas, float size_pixels = 16.0f)
{
    ImFontConfig config;
    config.SizePixels = size_pixels;
    config.OversampleH = 2;
    config.OversampleV = 1;
    config.PixelSnapH = true;
    
    return LoadFontFromMemory(atlas, g_font_hitmarker_medium, g_font_hitmarker_medium_size, size_pixels, &config);
}

// Helper function to load the bold font (title font)
inline ImFont* LoadHitmarkerBoldFont(ImFontAtlas* atlas, float size_pixels = 24.0f)
{
    ImFontConfig config;
    config.SizePixels = size_pixels;
    config.OversampleH = 2;
    config.OversampleV = 1;
    config.PixelSnapH = true;
    
    return LoadFontFromMemory(atlas, g_font_hitmarker_bold, g_font_hitmarker_bold_size, size_pixels, &config);
} 