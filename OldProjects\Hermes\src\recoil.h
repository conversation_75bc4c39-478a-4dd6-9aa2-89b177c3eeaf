#ifndef RECOIL_H
#define RECOIL_H

#include <string>
#include <vector>
#include <map>
#include <Windows.h>

// Recoil control system for use in the Operator application
// Provides functionality to compensate for weapon recoil

// Input device type for recoil control
enum RecoilInputDevice {
    RECOIL_INPUT_MOUSE      // Use mouse button (only supported device)
};

// Main class for recoil control functionality
class RecoilControl {
public:
    // Singleton access
    static RecoilControl& getInstance();
    
    // Delete copy and move constructors/assignments
    RecoilControl(const RecoilControl&) = delete;
    RecoilControl& operator=(const RecoilControl&) = delete;
    RecoilControl(RecoilControl&&) = delete;
    RecoilControl& operator=(RecoilControl&&) = delete;
    
    // Initialize the recoil control system
    bool initialize();
    
    // Shutdown and cleanup
    void shutdown();
    
    // Enable/disable recoil control
    void setEnabled(bool enabled);
    bool isEnabled() const;
    
    // Set target window handle
    void setTargetWindow(HWND targetWindow);
    HWND getTargetWindow() const;
    bool isTargetWindowActive() const;
    
    // Vertical recoil control (-50 to 50)
    // Negative values move cursor up, positive values move cursor down
    void setVerticalRecoil(int amount);
    int getVerticalRecoil() const;
    
    // Horizontal recoil control (-50 to 50)
    // Negative values move cursor left, positive values move cursor right
    void setHorizontalRecoil(int amount);
    int getHorizontalRecoil() const;
    
    // Smoothness setting (0.0 - 1.0)
    // Higher values make recoil correction more gradual
    void setSmoothness(float smoothness);
    float getSmoothness() const;
    
    // Randomization setting (0.0 - 1.0)
    // Higher values add more random variation to recoil correction
    void setRandomization(float randomness);
    float getRandomization() const;
    
    // Activation key configuration
    void setActivationKey(int key);
    int getActivationKey() const;
    
    // Toggle mode (on/off vs. hold)
    void setToggleMode(bool toggle);
    bool isToggleMode() const;
    
    // Input device selection (mouse only)
    void setInputDevice(RecoilInputDevice device);
    RecoilInputDevice getInputDevice() const;
    
    // Function to set the application window handle for the Operator menu
    void setApplicationWindow(HWND appWindow);
    
    // Function to check if the Operator menu is currently active
    bool isApplicationWindowActive() const;
    
    // Function to set/check if mouse is hovering over our application window
    void setMouseOverAppWindow(bool isOver);
    bool isMouseOverAppWindow() const;
    
    // Input handling
    void onMouseButtonEvent(int button, bool pressed);
    void onMouseMoveEvent();  // Handle mouse movement events
    
    // Process function - should be called in the main loop
    void process();
    
private:
    // Private constructor for singleton
    RecoilControl();
    
    // Helper functions
    void calculateNextRecoilValues(float& x, float& y);
    void applyRecoilCompensation(float x, float y);
    void resetRecoilState();
    
    // Member variables
    bool m_enabled;
    bool m_firing;
    bool m_toggleMode;
    int m_activationKey;
    HWND m_targetWindow;  // Target window for recoil control
    HWND m_applicationWindow;

    // Input device settings
    RecoilInputDevice m_inputDevice;  // Mouse only

    // Mouse movement tracking for interference detection
    unsigned long m_lastUserMouseTime;  // Last time user moved mouse
    bool m_recoilActive;  // Whether recoil is currently being applied
    bool m_ignoreNextMouseMove;  // Flag to ignore next mouse move (our own recoil)
    
    // Recoil control parameters
    int m_verticalRecoil;     // -50 to 50
    int m_horizontalRecoil;   // -50 to 50
    float m_smoothness;       // 0.0 to 1.0
    float m_randomization;    // 0.0 to 1.0
    
    // State tracking
    int m_currentStep;
    unsigned long m_lastUpdateTime;
    unsigned long m_fireStartTime;
    float m_currentX;
    float m_currentY;
    
    bool m_mouseOverAppWindow;  // Tracks if mouse is over our application
};

#endif // RECOIL_H 