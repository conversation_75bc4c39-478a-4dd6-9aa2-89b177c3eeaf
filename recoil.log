=== Fennec Client Debug Console ===
App starting...

Hardware-level mouse hook initialized successfully
Primary screen bounds: {X=0,Y=0,Width=2560,Height=1440}
Overlay window size: 2560x1440 at 0,0
MainWindow initialized successfully
NavigateTo<PERSON>ab called with button: OverviewButton
Button name: OverviewButton
Showing OverviewContent
NavigateToTab completed successfully
NavigateTo<PERSON>ab called with button: MNKButton
Button name: MNKButton
Showing MNKContent
NavigateToTab completed successfully
Recoil compensation STARTED
Recoil compensation STOPPED
Recoil compensation STARTED
Applying recoil: deltaX=0, deltaY=1, elapsed=114,1843ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=1, elapsed=129,0881ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=2, elapsed=145,0193ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 2
Applying recoil: deltaX=0, deltaY=2, elapsed=160,0323ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 2
Applying recoil: deltaX=0, deltaY=2, elapsed=180,0259ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 2
Applying recoil: deltaX=0, deltaY=3, elapsed=199,2214ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 2
Applying recoil: deltaX=0, deltaY=3, elapsed=214,2461ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 2
Applying recoil: deltaX=0, deltaY=3, elapsed=222,1484ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 3
Applying recoil: deltaX=0, deltaY=4, elapsed=238,193ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 4
Applying recoil: deltaX=0, deltaY=4, elapsed=254,2698ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 3
Applying recoil: deltaX=0, deltaY=4, elapsed=270,0763ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 3
Applying recoil: deltaX=0, deltaY=5, elapsed=290,0986ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 5
Applying recoil: deltaX=0, deltaY=5, elapsed=301,3043ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 4
Applying recoil: deltaX=0, deltaY=6, elapsed=316,1776ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 5
Applying recoil: deltaX=0, deltaY=6, elapsed=334,1233ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 6
Applying recoil: deltaX=0, deltaY=7, elapsed=351,0895ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 6
Applying recoil: deltaX=0, deltaY=7, elapsed=373,1932ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=388,4866ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=404,1127ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=419,6988ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=434,4208ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=9, elapsed=449,2838ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=9, elapsed=465,2708ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=9, elapsed=480,3585ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 7
Hardware-level injection: 0, 6
Hardware-level injection: 0, 8
Hardware-level injection: 0, 7
Recoil compensation STOPPED
Hardware-level injection: 0, 7
Hardware-level injection: 0, 8
Hardware-level injection: 0, 8
Hardware-level injection: 0, 8
Recoil compensation STARTED
Applying recoil: deltaX=0, deltaY=1, elapsed=63,1624ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Recoil compensation STOPPED
Recoil compensation STARTED
Applying recoil: deltaX=0, deltaY=1, elapsed=61,965ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=77,1424ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=92,333ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=107,6765ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=123,4986ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=139,3329ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=154,4532ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=169,5781ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=184,6953ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=200,7839ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=231,1637ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=246,7125ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=262,4135ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=277,6079ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=293,6394ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=309,1604ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=325,4196ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=341,2251ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=356,8921ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=387,3728ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=403,7991ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=419,025ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=434,6599ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=9, elapsed=450,9178ms
Attempting mouse input: deltaX=0, deltaY=9, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=9, elapsed=467,0062ms
Attempting mouse input: deltaX=0, deltaY=9, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=9, elapsed=483,4282ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=10, elapsed=498,7708ms
Attempting mouse input: deltaX=0, deltaY=9, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=10, elapsed=514,6752ms
Attempting mouse input: deltaX=0, deltaY=9, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=10, elapsed=529,8825ms
Attempting mouse input: deltaX=0, deltaY=10, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=11, elapsed=545,2176ms
Attempting mouse input: deltaX=0, deltaY=11, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=11, elapsed=560,6302ms
Attempting mouse input: deltaX=0, deltaY=11, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=11, elapsed=576,2839ms
Attempting mouse input: deltaX=0, deltaY=11, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=12, elapsed=592,1655ms
Attempting mouse input: deltaX=0, deltaY=11, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=12, elapsed=608,0605ms
Attempting mouse input: deltaX=0, deltaY=12, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=12, elapsed=623,7302ms
Attempting mouse input: deltaX=0, deltaY=12, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=13, elapsed=639,2503ms
Attempting mouse input: deltaX=0, deltaY=12, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=13, elapsed=654,6904ms
Attempting mouse input: deltaX=0, deltaY=12, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=13, elapsed=670,0769ms
Attempting mouse input: deltaX=0, deltaY=13, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=13, elapsed=685,5605ms
Attempting mouse input: deltaX=0, deltaY=12, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=14, elapsed=701,0507ms
Attempting mouse input: deltaX=0, deltaY=14, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=14, elapsed=716,354ms
Attempting mouse input: deltaX=0, deltaY=13, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=14, elapsed=731,8936ms
Attempting mouse input: deltaX=0, deltaY=13, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=15, elapsed=762,7004ms
Attempting mouse input: deltaX=0, deltaY=14, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=16, elapsed=792,9534ms
Attempting mouse input: deltaX=0, deltaY=15, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=16, elapsed=808,889ms
Attempting mouse input: deltaX=0, deltaY=15, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=16, elapsed=824,5103ms
Attempting mouse input: deltaX=0, deltaY=16, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=17, elapsed=840,4079ms
Attempting mouse input: deltaX=0, deltaY=17, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=17, elapsed=855,792ms
Attempting mouse input: deltaX=0, deltaY=16, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=17, elapsed=870,769ms
Attempting mouse input: deltaX=0, deltaY=16, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=18, elapsed=886,4164ms
Attempting mouse input: deltaX=0, deltaY=17, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=18, elapsed=902,5984ms
Attempting mouse input: deltaX=0, deltaY=17, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=18, elapsed=918,494ms
Attempting mouse input: deltaX=0, deltaY=18, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=18, elapsed=933,361ms
Attempting mouse input: deltaX=0, deltaY=17, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=19, elapsed=948,8596ms
Attempting mouse input: deltaX=0, deltaY=18, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=19, elapsed=964,4989ms
Attempting mouse input: deltaX=0, deltaY=19, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=19, elapsed=979,7784ms
Attempting mouse input: deltaX=0, deltaY=18, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=20, elapsed=995,5081ms
Attempting mouse input: deltaX=0, deltaY=19, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=20, elapsed=1011,4052ms
Attempting mouse input: deltaX=0, deltaY=20, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=20, elapsed=1026,6202ms
Attempting mouse input: deltaX=0, deltaY=19, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=21, elapsed=1041,9367ms
Attempting mouse input: deltaX=0, deltaY=20, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=21, elapsed=1057,3821ms
Attempting mouse input: deltaX=0, deltaY=20, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=21, elapsed=1072,9241ms
Attempting mouse input: deltaX=0, deltaY=20, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=22, elapsed=1088,2524ms
Attempting mouse input: deltaX=0, deltaY=22, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=22, elapsed=1103,5841ms
Attempting mouse input: deltaX=0, deltaY=21, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=22, elapsed=1119,2859ms
Attempting mouse input: deltaX=0, deltaY=22, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=23, elapsed=1134,7839ms
Attempting mouse input: deltaX=0, deltaY=23, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=23, elapsed=1150,2039ms
Attempting mouse input: deltaX=0, deltaY=22, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=23, elapsed=1165,4523ms
Attempting mouse input: deltaX=0, deltaY=23, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=24, elapsed=1181,2223ms
Attempting mouse input: deltaX=0, deltaY=24, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=24, elapsed=1197,3441ms
Attempting mouse input: deltaX=0, deltaY=24, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=24, elapsed=1212,4589ms
Attempting mouse input: deltaX=0, deltaY=23, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=24, elapsed=1227,7253ms
Attempting mouse input: deltaX=0, deltaY=24, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=25, elapsed=1243,5199ms
Attempting mouse input: deltaX=0, deltaY=25, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=25, elapsed=1259,0168ms
Attempting mouse input: deltaX=0, deltaY=26, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=25, elapsed=1275,0294ms
Attempting mouse input: deltaX=0, deltaY=24, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=26, elapsed=1290,4853ms
Attempting mouse input: deltaX=0, deltaY=25, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=26, elapsed=1305,475ms
Attempting mouse input: deltaX=0, deltaY=24, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=26, elapsed=1321,0053ms
Attempting mouse input: deltaX=0, deltaY=24, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=27, elapsed=1337,0001ms
Attempting mouse input: deltaX=0, deltaY=27, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=27, elapsed=1353,41ms
Attempting mouse input: deltaX=0, deltaY=27, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=27, elapsed=1368,5746ms
Attempting mouse input: deltaX=0, deltaY=27, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=28, elapsed=1384,4861ms
Attempting mouse input: deltaX=0, deltaY=29, useHardware=True
Mouse input result: success=True
Recoil compensation STOPPED
Hardware-level injection: 0, 0
Hardware-level injection: 0, 0
Hardware-level injection: 0, 0
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 1
Hardware-level injection: 0, 2
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 4
Hardware-level injection: 0, 5
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Hardware-level injection: 0, 7
Hardware-level injection: 0, 6
Hardware-level injection: 0, 7
Hardware-level injection: 0, 7
Hardware-level injection: 0, 8
Hardware-level injection: 0, 9
Hardware-level injection: 0, 9
Hardware-level injection: 0, 8
Hardware-level injection: 0, 9
Hardware-level injection: 0, 9
Hardware-level injection: 0, 10
Hardware-level injection: 0, 11
Hardware-level injection: 0, 11
Hardware-level injection: 0, 11
Hardware-level injection: 0, 11
Hardware-level injection: 0, 12
Hardware-level injection: 0, 12
Hardware-level injection: 0, 12
Hardware-level injection: 0, 12
Hardware-level injection: 0, 13
Hardware-level injection: 0, 12
Hardware-level injection: 0, 14
Hardware-level injection: 0, 13
Hardware-level injection: 0, 13
Hardware-level injection: 0, 14
Hardware-level injection: 0, 15
Hardware-level injection: 0, 15
Hardware-level injection: 0, 16
Hardware-level injection: 0, 17
Hardware-level injection: 0, 16
Hardware-level injection: 0, 16
Hardware-level injection: 0, 17
Hardware-level injection: 0, 17
Hardware-level injection: 0, 18
Hardware-level injection: 0, 17
Hardware-level injection: 0, 18
Hardware-level injection: 0, 19
Hardware-level injection: 0, 18
Hardware-level injection: 0, 19
Hardware-level injection: 0, 20
Hardware-level injection: 0, 19
Hardware-level injection: 0, 20
Hardware-level injection: 0, 20
Hardware-level injection: 0, 20
Recoil compensation STARTED
Applying recoil: deltaX=0, deltaY=1, elapsed=62,57ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=78,2881ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=93,2469ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=108,6813ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=124,2323ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=139,132ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=155,086ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=169,9934ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=185,8555ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=200,6231ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=216,537ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=232,2136ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=247,2118ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=262,8363ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=278,1745ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=294,4588ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=310,3376ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=325,561ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=340,6765ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=356,1672ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=371,2717ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=387,1379ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=402,6168ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=418,1161ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=433,6436ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=9, elapsed=448,888ms
Attempting mouse input: deltaX=0, deltaY=9, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=9, elapsed=464,6405ms
Attempting mouse input: deltaX=0, deltaY=9, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=9, elapsed=480,1924ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=10, elapsed=496,3274ms
Attempting mouse input: deltaX=0, deltaY=9, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=10, elapsed=512,1248ms
Attempting mouse input: deltaX=0, deltaY=9, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=10, elapsed=527,4443ms
Attempting mouse input: deltaX=0, deltaY=9, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=11, elapsed=543,0923ms
Attempting mouse input: deltaX=0, deltaY=10, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=11, elapsed=557,9609ms
Attempting mouse input: deltaX=0, deltaY=11, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=11, elapsed=573,7038ms
Attempting mouse input: deltaX=0, deltaY=10, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=12, elapsed=589,7818ms
Attempting mouse input: deltaX=0, deltaY=11, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=12, elapsed=605,521ms
Attempting mouse input: deltaX=0, deltaY=12, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=12, elapsed=621,3058ms
Attempting mouse input: deltaX=0, deltaY=11, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=12, elapsed=637,2193ms
Attempting mouse input: deltaX=0, deltaY=11, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=13, elapsed=653,2743ms
Attempting mouse input: deltaX=0, deltaY=12, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=13, elapsed=669,1597ms
Attempting mouse input: deltaX=0, deltaY=12, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=13, elapsed=684,8566ms
Attempting mouse input: deltaX=0, deltaY=13, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=14, elapsed=700,5439ms
Attempting mouse input: deltaX=0, deltaY=13, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=14, elapsed=716,775ms
Attempting mouse input: deltaX=0, deltaY=14, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=14, elapsed=732,4599ms
Attempting mouse input: deltaX=0, deltaY=14, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=15, elapsed=748,2014ms
Attempting mouse input: deltaX=0, deltaY=15, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=15, elapsed=764,4256ms
Attempting mouse input: deltaX=0, deltaY=15, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=15, elapsed=780,1792ms
Attempting mouse input: deltaX=0, deltaY=14, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=16, elapsed=795,6019ms
Attempting mouse input: deltaX=0, deltaY=16, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=16, elapsed=811,3585ms
Attempting mouse input: deltaX=0, deltaY=16, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=16, elapsed=826,4635ms
Attempting mouse input: deltaX=0, deltaY=15, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=17, elapsed=842,8853ms
Attempting mouse input: deltaX=0, deltaY=17, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=17, elapsed=858,2902ms
Attempting mouse input: deltaX=0, deltaY=16, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=17, elapsed=873,6992ms
Attempting mouse input: deltaX=0, deltaY=17, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=18, elapsed=889,063ms
Attempting mouse input: deltaX=0, deltaY=17, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=18, elapsed=904,2636ms
Attempting mouse input: deltaX=0, deltaY=17, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=18, elapsed=920,1476ms
Attempting mouse input: deltaX=0, deltaY=18, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=19, elapsed=935,7646ms
Attempting mouse input: deltaX=0, deltaY=19, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=19, elapsed=951,1004ms
Attempting mouse input: deltaX=0, deltaY=18, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=19, elapsed=966,975ms
Attempting mouse input: deltaX=0, deltaY=19, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=20, elapsed=982,7062ms
Attempting mouse input: deltaX=0, deltaY=20, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=20, elapsed=997,7349ms
Attempting mouse input: deltaX=0, deltaY=19, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=20, elapsed=1012,8239ms
Attempting mouse input: deltaX=0, deltaY=19, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=20, elapsed=1028,2187ms
Attempting mouse input: deltaX=0, deltaY=19, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=21, elapsed=1043,8178ms
Attempting mouse input: deltaX=0, deltaY=20, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=21, elapsed=1059,4698ms
Attempting mouse input: deltaX=0, deltaY=20, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=21, elapsed=1074,5776ms
Attempting mouse input: deltaX=0, deltaY=21, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=22, elapsed=1090,1844ms
Attempting mouse input: deltaX=0, deltaY=21, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=22, elapsed=1106,4836ms
Attempting mouse input: deltaX=0, deltaY=21, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=22, elapsed=1121,5051ms
Attempting mouse input: deltaX=0, deltaY=21, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=23, elapsed=1136,9634ms
Attempting mouse input: deltaX=0, deltaY=23, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=23, elapsed=1152,8084ms
Attempting mouse input: deltaX=0, deltaY=23, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=23, elapsed=1168,57ms
Attempting mouse input: deltaX=0, deltaY=23, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=24, elapsed=1183,6742ms
Attempting mouse input: deltaX=0, deltaY=23, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=24, elapsed=1199,5001ms
Attempting mouse input: deltaX=0, deltaY=24, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=24, elapsed=1215,5497ms
Attempting mouse input: deltaX=0, deltaY=23, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=25, elapsed=1231,1159ms
Attempting mouse input: deltaX=0, deltaY=24, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=25, elapsed=1247,0705ms
Attempting mouse input: deltaX=0, deltaY=24, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=25, elapsed=1262,5903ms
Attempting mouse input: deltaX=0, deltaY=25, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=26, elapsed=1278,791ms
Attempting mouse input: deltaX=0, deltaY=27, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=26, elapsed=1294,9897ms
Attempting mouse input: deltaX=0, deltaY=25, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=26, elapsed=1311,2898ms
Attempting mouse input: deltaX=0, deltaY=25, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=27, elapsed=1326,9656ms
Attempting mouse input: deltaX=0, deltaY=26, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=27, elapsed=1342,6502ms
Attempting mouse input: deltaX=0, deltaY=27, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=27, elapsed=1358,0925ms
Attempting mouse input: deltaX=0, deltaY=28, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=27, elapsed=1373,1194ms
Attempting mouse input: deltaX=0, deltaY=28, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=28, elapsed=1389,3186ms
Attempting mouse input: deltaX=0, deltaY=26, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=28, elapsed=1404,7456ms
Attempting mouse input: deltaX=0, deltaY=29, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=28, elapsed=1420,6549ms
Attempting mouse input: deltaX=0, deltaY=26, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=29, elapsed=1435,7153ms
Attempting mouse input: deltaX=0, deltaY=29, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=29, elapsed=1451,4859ms
Attempting mouse input: deltaX=0, deltaY=27, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=29, elapsed=1467,1041ms
Attempting mouse input: deltaX=0, deltaY=30, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=30, elapsed=1482,6328ms
Attempting mouse input: deltaX=0, deltaY=28, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=30, elapsed=1498,5111ms
Attempting mouse input: deltaX=0, deltaY=29, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=30, elapsed=1513,4596ms
Attempting mouse input: deltaX=0, deltaY=31, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=31, elapsed=1529,4481ms
Attempting mouse input: deltaX=0, deltaY=32, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=31, elapsed=1544,9713ms
Attempting mouse input: deltaX=0, deltaY=30, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=31, elapsed=1560,47ms
Attempting mouse input: deltaX=0, deltaY=31, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=32, elapsed=1575,5329ms
Attempting mouse input: deltaX=0, deltaY=31, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=32, elapsed=1590,6781ms
Attempting mouse input: deltaX=0, deltaY=32, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=32, elapsed=1607,3498ms
Attempting mouse input: deltaX=0, deltaY=31, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=33, elapsed=1622,7756ms
Attempting mouse input: deltaX=0, deltaY=32, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=33, elapsed=1638,1525ms
Attempting mouse input: deltaX=0, deltaY=34, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=33, elapsed=1654,6114ms
Attempting mouse input: deltaX=0, deltaY=31, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=34, elapsed=1670,4882ms
Attempting mouse input: deltaX=0, deltaY=33, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=34, elapsed=1685,2235ms
Attempting mouse input: deltaX=0, deltaY=35, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=34, elapsed=1701,1263ms
Attempting mouse input: deltaX=0, deltaY=35, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=34, elapsed=1716,7286ms
Attempting mouse input: deltaX=0, deltaY=35, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=35, elapsed=1732,1701ms
Attempting mouse input: deltaX=0, deltaY=35, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=35, elapsed=1747,3887ms
Attempting mouse input: deltaX=0, deltaY=35, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=35, elapsed=1763,4766ms
Attempting mouse input: deltaX=0, deltaY=34, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=36, elapsed=1779,1494ms
Attempting mouse input: deltaX=0, deltaY=35, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=36, elapsed=1794,2113ms
Attempting mouse input: deltaX=0, deltaY=34, useHardware=True
Mouse input result: success=True
Recoil compensation STOPPED
Hardware-level injection: 0, 22
Hardware-level injection: 0, 21
Hardware-level injection: 0, 22
Hardware-level injection: 0, 23
Hardware-level injection: 0, 22
Hardware-level injection: 0, 23
Hardware-level injection: 0, 24
Hardware-level injection: 0, 24
Hardware-level injection: 0, 23
Hardware-level injection: 0, 24
Hardware-level injection: 0, 25
Hardware-level injection: 0, 26
Hardware-level injection: 0, 24
Hardware-level injection: 0, 25
Hardware-level injection: 0, 24
Hardware-level injection: 0, 24
Hardware-level injection: 0, 27
Hardware-level injection: 0, 27
Hardware-level injection: 0, 27
Hardware-level injection: 0, 29
Hardware-level injection: 0, 0
Hardware-level injection: 0, 0
Hardware-level injection: 0, 0
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Hardware-level injection: 0, 3
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 4
Hardware-level injection: 0, 3
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Hardware-level injection: 0, 5
Hardware-level injection: 0, 7
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Hardware-level injection: 0, 8
Hardware-level injection: 0, 8
Hardware-level injection: 0, 8
Hardware-level injection: 0, 9
Hardware-level injection: 0, 9
Hardware-level injection: 0, 8
Hardware-level injection: 0, 9
Hardware-level injection: 0, 9
Hardware-level injection: 0, 9
Hardware-level injection: 0, 10
Hardware-level injection: 0, 11
Hardware-level injection: 0, 10
Hardware-level injection: 0, 11
Hardware-level injection: 0, 12
Hardware-level injection: 0, 11
Hardware-level injection: 0, 11
Hardware-level injection: 0, 12
Hardware-level injection: 0, 12
Hardware-level injection: 0, 13
Hardware-level injection: 0, 13
Hardware-level injection: 0, 14
Hardware-level injection: 0, 14
Hardware-level injection: 0, 15
Hardware-level injection: 0, 15
Hardware-level injection: 0, 14
Hardware-level injection: 0, 16
Hardware-level injection: 0, 16
Hardware-level injection: 0, 15
Hardware-level injection: 0, 17
Hardware-level injection: 0, 16
Hardware-level injection: 0, 17
Hardware-level injection: 0, 17
Hardware-level injection: 0, 17
Hardware-level injection: 0, 18
Hardware-level injection: 0, 19
Hardware-level injection: 0, 18
Hardware-level injection: 0, 19
Hardware-level injection: 0, 20
Hardware-level injection: 0, 19
Hardware-level injection: 0, 19
Hardware-level injection: 0, 19
Hardware-level injection: 0, 20
Hardware-level injection: 0, 20
Hardware-level injection: 0, 21
Hardware-level injection: 0, 21
Hardware-level injection: 0, 21
Hardware-level injection: 0, 21
Hardware-level injection: 0, 23
Hardware-level injection: 0, 23
Hardware-level injection: 0, 23
Hardware-level injection: 0, 23
Hardware-level injection: 0, 24
Hardware-level injection: 0, 23
Hardware-level injection: 0, 24
Hardware-level injection: 0, 24
Hardware-level injection: 0, 25
Hardware-level injection: 0, 27
Hardware-level injection: 0, 25
Hardware-level injection: 0, 25
Hardware-level injection: 0, 26
Hardware-level injection: 0, 27
Hardware-level injection: 0, 28
Hardware-level injection: 0, 28
Hardware-level injection: 0, 26
Hardware-level injection: 0, 29
Hardware-level injection: 0, 26
Hardware-level injection: 0, 29
Hardware-level injection: 0, 27
Hardware-level injection: 0, 30
Hardware-level injection: 0, 28
Hardware-level injection: 0, 29
Hardware-level injection: 0, 31
Hardware-level injection: 0, 32
Hardware-level injection: 0, 30
Hardware-level injection: 0, 31
Hardware-level injection: 0, 31
Hardware-level injection: 0, 32
Hardware-level injection: 0, 31
Hardware-level injection: 0, 32
Hardware-level injection: 0, 34
Hardware-level injection: 0, 31
Hardware-level injection: 0, 33
Hardware-level injection: 0, 35
Hardware-level injection: 0, 35
Hardware-level injection: 0, 35
Hardware-level injection: 0, 35
Hardware-level injection: 0, 35
Hardware-level injection: 0, 34
Hardware-level injection: 0, 35
Hardware-level injection: 0, 34
Recoil compensation STARTED
Applying recoil: deltaX=0, deltaY=1, elapsed=170,9925ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=186,8881ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=202,4601ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=217,7903ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=233,0353ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=249,6431ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=264,8983ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=280,6961ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=296,6579ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=311,7227ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=327,6586ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=343,5359ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=359,2447ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=375,1089ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=390,7626ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=405,6532ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=421,048ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=437,4981ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=453,3972ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=468,4417ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=483,6436ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=499,1291ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=515,0727ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=530,478ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=545,8728ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=561,0957ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=577,2534ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=592,526ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=608,1949ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=624,2333ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=639,6173ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=655,6384ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=670,7226ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=686,7252ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=701,7688ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=717,8866ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=733,5152ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=749,4877ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=764,6992ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=779,7828ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=795,8953ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=811,3198ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=826,7942ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=857,7347ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=873,4722ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=889,5825ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=905,0216ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=920,6605ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=936,1647ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=951,7251ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=982,9704ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=999,407ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1014,8314ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1030,2244ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1045,9048ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1061,493ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1076,6424ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Recoil compensation STOPPED
Hardware-level injection: 0, 1
Hardware-level injection: 0, 0
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Hardware-level injection: 0, 0
Hardware-level injection: 0, 1
Hardware-level injection: 0, 0
Hardware-level injection: 0, 0
Hardware-level injection: 0, 1
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 1
MainWindow hidden with INSERT key
Hardware-level injection: 0, 1
Hardware-level injection: 0, 2
Hardware-level injection: 0, 1
Hardware-level injection: 0, 2
Hardware-level injection: 0, 1
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 2
Hardware-level injection: 0, 3
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 4
Hardware-level injection: 0, 3
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Recoil compensation STARTED
Applying recoil: deltaX=0, deltaY=1, elapsed=172,5096ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=1, elapsed=188,4754ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=203,7173ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=219,8613ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=235,3092ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=250,7976ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=266,0968ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=1, elapsed=281,6868ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Applying recoil: deltaX=0, deltaY=1, elapsed=297,5544ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=1, elapsed=313,7505ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Applying recoil: deltaX=0, deltaY=1, elapsed=329,2351ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Applying recoil: deltaX=0, deltaY=2, elapsed=344,7056ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=360,6721ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=375,7368ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=391,8648ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=406,6766ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=422,963ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=438,431ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=453,4469ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=468,6905ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=483,9624ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=499,7391ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=514,8542ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=530,7748ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=546,0786ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=561,4433ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=576,6553ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=592,8642ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=609,4144ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=624,8186ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=640,4849ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=656,3226ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=671,5571ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=686,9546ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=703,5922ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=719,1915ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=734,567ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=751,0432ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=766,873ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=781,8268ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=797,8552ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=813,6592ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=829,2105ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=843,8855ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=860,0248ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=874,7139ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=890,5474ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=905,6786ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=921,2644ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=936,9329ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=952,5108ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=968,0053ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=983,6407ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=999,2974ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1014,1173ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1030,2584ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1045,9572ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1061,1919ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1076,6424ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1092,1775ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1107,8592ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1123,466ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1138,5946ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1154,6206ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1170,2211ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1186,2801ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1201,9793ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1217,1222ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1232,378ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1247,9103ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1263,9277ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1279,7594ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1295,7683ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1311,3818ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1327,4365ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=1343,8098ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=1359,7272ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=1375,046ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=1390,2161ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=1406,0896ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Recoil compensation STOPPED
Hardware-level injection: 0, 0
Hardware-level injection: 0, 1
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 1
Hardware-level injection: 0, 2
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 3
Hardware-level injection: 0, 2
Hardware-level injection: 0, 3
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 3
Hardware-level injection: 0, 2
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 5
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 4
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Hardware-level injection: 0, 5
Hardware-level injection: 0, 6
Hardware-level injection: 0, 5
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Hardware-level injection: 0, 7
Hardware-level injection: 0, 6
Hardware-level injection: 0, 7
Hardware-level injection: 0, 6
Hardware-level injection: 0, 7
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Hardware-level injection: 0, 7
Hardware-level injection: 0, 7
Hardware-level injection: 0, 7
Hardware-level injection: 0, 8
Hardware-level injection: 0, 7
Hardware-level injection: 0, 8
Recoil compensation STARTED
Applying recoil: deltaX=0, deltaY=1, elapsed=171,506ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Applying recoil: deltaX=0, deltaY=1, elapsed=187,1446ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=1, elapsed=203,0082ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Applying recoil: deltaX=0, deltaY=1, elapsed=218,3755ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Applying recoil: deltaX=0, deltaY=1, elapsed=234,3046ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=1, elapsed=249,2885ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Applying recoil: deltaX=0, deltaY=1, elapsed=265,1023ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=1, elapsed=280,5164ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Applying recoil: deltaX=0, deltaY=1, elapsed=296,2858ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=311,4661ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=327,528ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=342,6443ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=358,6112ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=373,5327ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=389,4577ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=404,5636ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=419,8502ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=435,7236ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=450,9613ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=466,3179ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=482,1215ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=497,5905ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=513,6933ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=528,5057ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=544,3665ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=559,4604ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Recoil compensation STOPPED
Hardware-level injection: 0, 1
Hardware-level injection: 0, 0
Hardware-level injection: 0, 0
Hardware-level injection: 0, 2
Hardware-level injection: 0, 1
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Recoil compensation STARTED
Applying recoil: deltaX=0, deltaY=1, elapsed=172,0114ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=187,2229ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=217,8009ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=233,8403ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=249,1372ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=264,9318ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=280,4579ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=296,3666ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 2
Applying recoil: deltaX=0, deltaY=1, elapsed=311,4908ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Applying recoil: deltaX=0, deltaY=1, elapsed=326,5738ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Applying recoil: deltaX=0, deltaY=2, elapsed=342,3265ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=358,3029ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 2
Applying recoil: deltaX=0, deltaY=2, elapsed=374,224ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Applying recoil: deltaX=0, deltaY=2, elapsed=390,4706ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Applying recoil: deltaX=0, deltaY=2, elapsed=421,5204ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 3
Applying recoil: deltaX=0, deltaY=2, elapsed=436,4776ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 2
Hardware-level injection: 0, 1
Applying recoil: deltaX=0, deltaY=2, elapsed=451,9984ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=468,4739ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=483,9655ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=514,997ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=530,4629ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=546,0175ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=561,3515ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=577,393ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=593,4943ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=610,1908ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=624,9508ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=3, elapsed=641,0046ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Applying recoil: deltaX=0, deltaY=3, elapsed=657,0684ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=4, elapsed=672,7347ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=4, elapsed=688,3147ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=4, elapsed=704,0319ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=719,3334ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=734,465ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=4, elapsed=750,0768ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=766,1657ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=782,2581ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=797,6217ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=813,0986ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=829,2093ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=844,3261ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Applying recoil: deltaX=0, deltaY=5, elapsed=860,3521ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Applying recoil: deltaX=0, deltaY=5, elapsed=875,6306ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 2
Hardware-level injection: 0, 1
Applying recoil: deltaX=0, deltaY=5, elapsed=890,4307ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Hardware-level injection: 0, 3
Hardware-level injection: 0, 2
Applying recoil: deltaX=0, deltaY=5, elapsed=905,9201ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 3
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 2
Hardware-level injection: 0, 3
Hardware-level injection: 0, 4
Applying recoil: deltaX=0, deltaY=5, elapsed=921,9858ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 4
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 4
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Applying recoil: deltaX=0, deltaY=5, elapsed=952,2405ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 5
Applying recoil: deltaX=0, deltaY=5, elapsed=968,0704ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 5
Applying recoil: deltaX=0, deltaY=5, elapsed=983,8304ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=999,4753ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1015,1679ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1031,2662ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1047,5169ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1062,5934ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1078,0779ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1093,926ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1109,0271ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1124,5671ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 4
Applying recoil: deltaX=0, deltaY=6, elapsed=1156,2164ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1186,1512ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1201,9236ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1217,0505ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1232,279ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1248,6011ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1264,3177ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1279,5182ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1296,1813ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1311,6733ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=7, elapsed=1326,648ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 5
Applying recoil: deltaX=0, deltaY=8, elapsed=1342,2167ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=1358,1612ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Applying recoil: deltaX=0, deltaY=8, elapsed=1373,2931ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 5
Applying recoil: deltaX=0, deltaY=8, elapsed=1389,3023ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=1404,1174ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 5
Applying recoil: deltaX=0, deltaY=8, elapsed=1419,4194ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=8, elapsed=1434,9046ms
Attempting mouse input: deltaX=0, deltaY=8, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 6
Applying recoil: deltaX=0, deltaY=8, elapsed=1450,1481ms
Attempting mouse input: deltaX=0, deltaY=7, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 6
Recoil compensation STOPPED
Hardware-level injection: 0, 5
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Hardware-level injection: 0, 7
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Hardware-level injection: 0, 7
Hardware-level injection: 0, 7
Hardware-level injection: 0, 7
Hardware-level injection: 0, 7
Hardware-level injection: 0, 7
Hardware-level injection: 0, 7
Hardware-level injection: 0, 7
Hardware-level injection: 0, 7
Hardware-level injection: 0, 8
Hardware-level injection: 0, 8
Hardware-level injection: 0, 8
Hardware-level injection: 0, 8
Hardware-level injection: 0, 8
Hardware-level injection: 0, 7
Recoil compensation STARTED
Applying recoil: deltaX=0, deltaY=1, elapsed=171,7284ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=1, elapsed=187,5181ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=202,7443ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=218,3586ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=1, elapsed=233,3712ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=248,9297ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=264,4857ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 1
Applying recoil: deltaX=0, deltaY=1, elapsed=280,6054ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=1, elapsed=295,481ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=1, elapsed=312,2708ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=1, elapsed=327,4131ms
Attempting mouse input: deltaX=0, deltaY=0, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=342,5076ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=358,2419ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=373,2803ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=388,7043ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=404,1626ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=420,137ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=435,8448ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=2, elapsed=451,3777ms
Attempting mouse input: deltaX=0, deltaY=1, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=466,6226ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=2, elapsed=482,6143ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Hardware-level injection: 0, 0
Applying recoil: deltaX=0, deltaY=2, elapsed=498,7076ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=513,9285ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=529,6815ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=545,4803ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=560,8446ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=576,3025ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=591,5239ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=607,2125ms
Attempting mouse input: deltaX=0, deltaY=2, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=622,7724ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=638,5015ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=3, elapsed=653,4177ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=669,3276ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=684,6593ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=700,3262ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=716,6877ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=731,8347ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=747,4188ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=762,7118ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=778,1859ms
Attempting mouse input: deltaX=0, deltaY=3, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=794,0024ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=809,7017ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=4, elapsed=825,753ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=840,2235ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=855,0604ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=871,038ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=886,5796ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=901,3863ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=916,455ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=931,8338ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=946,8026ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=962,407ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=978,5672ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=5, elapsed=994,1196ms
Attempting mouse input: deltaX=0, deltaY=4, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1009,5951ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1025,1996ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1040,4976ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1056,4957ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1071,7555ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1086,985ms
Attempting mouse input: deltaX=0, deltaY=5, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1102,408ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Applying recoil: deltaX=0, deltaY=6, elapsed=1118,5224ms
Attempting mouse input: deltaX=0, deltaY=6, useHardware=True
Mouse input result: success=True
Recoil compensation STOPPED
Hardware-level injection: 0, 0
Hardware-level injection: 0, 1
Hardware-level injection: 0, 0
Hardware-level injection: 0, 2
Hardware-level injection: 0, 1
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Hardware-level injection: 0, 1
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 2
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 2
Hardware-level injection: 0, 3
Hardware-level injection: 0, 2
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 3
Hardware-level injection: 0, 4
Hardware-level injection: 0, 3
Hardware-level injection: 0, 4
Hardware-level injection: 0, 3
Hardware-level injection: 0, 4
Hardware-level injection: 0, 3
Hardware-level injection: 0, 4
Hardware-level injection: 0, 3
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 5
Hardware-level injection: 0, 4
Hardware-level injection: 0, 5
Hardware-level injection: 0, 4
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 4
Hardware-level injection: 0, 5
Hardware-level injection: 0, 4
Hardware-level injection: 0, 4
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 5
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
Hardware-level injection: 0, 5
Hardware-level injection: 0, 6
Hardware-level injection: 0, 6
