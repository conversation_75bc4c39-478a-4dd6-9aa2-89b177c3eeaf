cmake_minimum_required(VERSION 3.20)
project(FennecClient.Native)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Define the library
add_library(FennecClient.Native SHARED
    RecoilEngine.cpp
    RecoilEngine.h
    pch.h
)

# Set preprocessor definitions
target_compile_definitions(FennecClient.Native PRIVATE
    FENNECCLIENTNATIVE_EXPORTS
    WIN32_LEAN_AND_MEAN
    NOMINMAX
)

# Link system libraries
target_link_libraries(FennecClient.Native
    user32
    kernel32
    psapi
)

# Set target properties
set_target_properties(FennecClient.Native PROPERTIES
    OUTPUT_NAME "FennecClient.Native"
    PREFIX ""
    SUFFIX ".dll"
)

# Enable precompiled headers
target_precompile_headers(FennecClient.Native PRIVATE pch.h)

# Set compiler flags for optimization and debugging
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    target_compile_options(FennecClient.Native PRIVATE /O2 /GL)
    target_link_options(FennecClient.Native PRIVATE /LTCG)
else()
    target_compile_options(FennecClient.Native PRIVATE /Od /Zi)
endif()

# Set warning level
target_compile_options(FennecClient.Native PRIVATE /W3)

# Copy DLL to output directory after build
add_custom_command(TARGET FennecClient.Native POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    $<TARGET_FILE:FennecClient.Native>
    ${CMAKE_SOURCE_DIR}/../FennecClient.App/bin/$<CONFIG>/net8.0-windows/
    COMMENT "Copying FennecClient.Native.dll to App output directory"
)
