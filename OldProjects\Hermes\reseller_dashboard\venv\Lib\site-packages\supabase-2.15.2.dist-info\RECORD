../../Scripts/tests.exe,sha256=5pvco0yS6hP8WSBe7CD6xpJ1X4Nhjx_XibPymGCJ4r4,108431
supabase-2.15.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
supabase-2.15.2.dist-info/LICENSE,sha256=M03Wgg4urqsgZOfFkAG4EFZnKKKKQafB2_abvuF9CTY,1065
supabase-2.15.2.dist-info/METADATA,sha256=0lQXcVL7Nvd7nQA4lUIvVUcF-84wIlXDyW2cF5CCgho,11062
supabase-2.15.2.dist-info/RECORD,,
supabase-2.15.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
supabase-2.15.2.dist-info/WHEEL,sha256=Nq82e9rUAnEjt98J6MlVmMCZb-t9cYE2Ir1kpBmnWfs,88
supabase-2.15.2.dist-info/entry_points.txt,sha256=F4onP9kSP0FoU2sCoOxrYbU-c60KwlZ_0quCskleaKg,50
supabase/__init__.py,sha256=OtLvn1Z0ddJZH3qSeT0LvbfD8wbiydcwD62vwF36V9o,2313
supabase/__pycache__/__init__.cpython-313.pyc,,
supabase/__pycache__/client.cpython-313.pyc,,
supabase/__pycache__/types.cpython-313.pyc,,
supabase/__pycache__/version.cpython-313.pyc,,
supabase/_async/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
supabase/_async/__pycache__/__init__.cpython-313.pyc,,
supabase/_async/__pycache__/auth_client.cpython-313.pyc,,
supabase/_async/__pycache__/client.cpython-313.pyc,,
supabase/_async/auth_client.py,sha256=MSjAgot_BUstxKP_GRkwDkwEG_cdX9DA5OfRDauHfgE,2129
supabase/_async/client.py,sha256=V5OOrfmbB4OtBgmnRMMfDwzmJ1aT3L5d9_RECWImH2E,11769
supabase/_sync/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
supabase/_sync/__pycache__/__init__.cpython-313.pyc,,
supabase/_sync/__pycache__/auth_client.cpython-313.pyc,,
supabase/_sync/__pycache__/client.cpython-313.pyc,,
supabase/_sync/auth_client.py,sha256=ZVMonayQJVUatWxcY20QX1Ke9KBYngd4KyWGoSH0KcU,2116
supabase/_sync/client.py,sha256=7EnQzAk6U5QUGOU2FUMmbEgau6pNROLVY6frCvmjz0o,11559
supabase/client.py,sha256=NciJGgxVMZHIrGCrN-_iCDEjw_C2s2eYYaLUvkskOGE,2075
supabase/lib/__init__.py,sha256=hBGVFLg5RVk6liHGIUuak1crNBiz5m-mPvvdxv8nmNU,67
supabase/lib/__pycache__/__init__.cpython-313.pyc,,
supabase/lib/__pycache__/client_options.cpython-313.pyc,,
supabase/lib/client_options.py,sha256=e4zdfZLEHclLc0Vw_bABZYDoTY0E4G6uzDxPDiwuQBI,6798
supabase/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
supabase/types.py,sha256=dvqNhqMQiGUjHJ8geOngvXlJ5wntGapM_pGnMBjwVrM,178
supabase/version.py,sha256=0qG9Pbok9tR8XlJT_kS769TRrvBQrDdLYLrNUMU_6FI,53
