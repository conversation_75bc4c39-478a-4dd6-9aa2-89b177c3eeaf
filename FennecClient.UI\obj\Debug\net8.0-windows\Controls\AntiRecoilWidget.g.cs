﻿#pragma checksum "..\..\..\..\Controls\AntiRecoilWidget.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F25ED0D7B310AE93B43686696081C54FAE453AE2"
//------------------------------------------------------------------------------
// <auto-generated>
//     Dieser Code wurde von einem Tool generiert.
//     Laufzeitversion:4.0.30319.42000
//
//     Änderungen an dieser Datei können falsches Verhalten verursachen und gehen verloren, wenn
//     der Code erneut generiert wird.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FennecClient.UI.Controls {
    
    
    /// <summary>
    /// AntiRecoilWidget
    /// </summary>
    public partial class AntiRecoilWidget : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 273 "..\..\..\..\Controls\AntiRecoilWidget.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ContentArea;
        
        #line default
        #line hidden
        
        
        #line 291 "..\..\..\..\Controls\AntiRecoilWidget.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton MouseRecoilToggle;
        
        #line default
        #line hidden
        
        
        #line 335 "..\..\..\..\Controls\AntiRecoilWidget.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HorizontalValue;
        
        #line default
        #line hidden
        
        
        #line 359 "..\..\..\..\Controls\AntiRecoilWidget.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider HorizontalSlider;
        
        #line default
        #line hidden
        
        
        #line 383 "..\..\..\..\Controls\AntiRecoilWidget.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider VerticalSlider;
        
        #line default
        #line hidden
        
        
        #line 407 "..\..\..\..\Controls\AntiRecoilWidget.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider SensitivitySlider;
        
        #line default
        #line hidden
        
        
        #line 431 "..\..\..\..\Controls\AntiRecoilWidget.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider DelaySlider;
        
        #line default
        #line hidden
        
        
        #line 447 "..\..\..\..\Controls\AntiRecoilWidget.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ActivationKeyCombo;
        
        #line default
        #line hidden
        
        
        #line 570 "..\..\..\..\Controls\AntiRecoilWidget.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RapidFireToggle;
        
        #line default
        #line hidden
        
        
        #line 610 "..\..\..\..\Controls\AntiRecoilWidget.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider RapidFireSlider;
        
        #line default
        #line hidden
        
        
        #line 615 "..\..\..\..\Controls\AntiRecoilWidget.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox RapidFireKeyCombo;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FennecClient.UI;V1.0.0.0;component/controls/antirecoilwidget.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\AntiRecoilWidget.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ContentArea = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 2:
            this.MouseRecoilToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 3:
            this.HorizontalValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.HorizontalSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 5:
            this.VerticalSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 6:
            this.SensitivitySlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 7:
            this.DelaySlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 8:
            this.ActivationKeyCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.RapidFireToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 10:
            this.RapidFireSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 11:
            this.RapidFireKeyCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

