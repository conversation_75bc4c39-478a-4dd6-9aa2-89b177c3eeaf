<UserControl x:Class="FennecClient.UI.Controls.XboxRecoilWidget"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="XboxRecoilConverter"/>
        
        <!-- Slider Track Button Style -->
        <Style x:Key="SliderTrackButton" TargetType="RepeatButton">
            <Setter Property="SnapsToDevicePixels" Value="true"/>
            <Setter Property="OverridesDefaultStyle" Value="true"/>
            <Setter Property="IsTabStop" Value="false"/>
            <Setter Property="Focusable" Value="false"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="RepeatButton">
                        <Border Background="{TemplateBinding Background}" Height="4" CornerRadius="2"/>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Slider Thumb -->
        <Style x:Key="ModernSliderThumb" TargetType="Thumb">
            <Setter Property="SnapsToDevicePixels" Value="true"/>
            <Setter Property="OverridesDefaultStyle" Value="true"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Thumb">
                        <Grid>
                            <!-- Outer Neon Glow -->
                            <Ellipse Name="OuterGlow" Width="40" Height="40" Opacity="0">
                                <Ellipse.Fill>
                                    <RadialGradientBrush>
                                        <GradientStop Color="#ff0040" Offset="0"/>
                                        <GradientStop Color="Transparent" Offset="1"/>
                                    </RadialGradientBrush>
                                </Ellipse.Fill>
                                <Ellipse.Effect>
                                    <BlurEffect Radius="8"/>
                                </Ellipse.Effect>
                            </Ellipse>
                            
                            <!-- Glass Thumb Container -->
                            <Border Name="GlassContainer" Width="24" Height="24" CornerRadius="12">
                                <Border.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#60ffffff" Offset="0"/>
                                        <GradientStop Color="#20ffffff" Offset="0.5"/>
                                        <GradientStop Color="#10ffffff" Offset="1"/>
                                    </LinearGradientBrush>
                                </Border.Background>
                                <Border.BorderBrush>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#80fa0000" Offset="0"/>
                                        <GradientStop Color="#ff0040" Offset="1"/>
                                    </LinearGradientBrush>
                                </Border.BorderBrush>
                                <Border.BorderThickness>2</Border.BorderThickness>
                                <Border.Effect>
                                    <DropShadowEffect Color="#fa0000" Direction="0" ShadowDepth="0" BlurRadius="12" Opacity="0.8"/>
                                </Border.Effect>
                            </Border>
                            
                            <!-- Inner Neon Circle -->
                            <Ellipse Name="InnerNeon" Width="12" Height="12" Fill="#ff0040" Opacity="0.9">
                                <Ellipse.Effect>
                                    <BlurEffect Radius="2"/>
                                </Ellipse.Effect>
                            </Ellipse>
                            
                            <!-- Center Dot -->
                            <Ellipse Width="6" Height="6" Fill="White" Opacity="0.9"/>
                        </Grid>
                        
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="OuterGlow" 
                                                           Storyboard.TargetProperty="Opacity" 
                                                           To="0.6" Duration="0:0:0.3"/>
                                            <DoubleAnimation Storyboard.TargetName="GlassContainer" 
                                                           Storyboard.TargetProperty="Width" 
                                                           To="28" Duration="0:0:0.3"/>
                                            <DoubleAnimation Storyboard.TargetName="GlassContainer" 
                                                           Storyboard.TargetProperty="Height" 
                                                           To="28" Duration="0:0:0.3"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="OuterGlow" 
                                                           Storyboard.TargetProperty="Opacity" 
                                                           To="0" Duration="0:0:0.3"/>
                                            <DoubleAnimation Storyboard.TargetName="GlassContainer" 
                                                           Storyboard.TargetProperty="Width" 
                                                           To="24" Duration="0:0:0.3"/>
                                            <DoubleAnimation Storyboard.TargetName="GlassContainer" 
                                                           Storyboard.TargetProperty="Height" 
                                                           To="24" Duration="0:0:0.3"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                            
                            <Trigger Property="IsDragging" Value="True">
                                <Setter TargetName="OuterGlow" Property="Opacity" Value="1"/>
                                <Setter TargetName="GlassContainer" Property="Width" Value="32"/>
                                <Setter TargetName="GlassContainer" Property="Height" Value="32"/>
                                <Setter TargetName="InnerNeon" Property="Width" Value="16"/>
                                <Setter TargetName="InnerNeon" Property="Height" Value="16"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Slider Style -->
        <Style x:Key="ModernSlider" TargetType="Slider">
            <Setter Property="SnapsToDevicePixels" Value="true"/>
            <Setter Property="OverridesDefaultStyle" Value="true"/>
            <Style.Triggers>
                <Trigger Property="Orientation" Value="Horizontal">
                    <Setter Property="MinWidth" Value="100"/>
                    <Setter Property="MinHeight" Value="40"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="Slider">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    
                                    <!-- Neon Track Background -->
                                    <Border Grid.Row="1" Height="6" CornerRadius="3" Margin="16,0">
                                        <Border.Background>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                                <GradientStop Color="#0a0a0a" Offset="0"/>
                                                <GradientStop Color="#1a1a1a" Offset="0.5"/>
                                                <GradientStop Color="#0a0a0a" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Border.Background>
                                        <Border.BorderBrush>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                                <GradientStop Color="#333" Offset="0"/>
                                                <GradientStop Color="#555" Offset="0.5"/>
                                                <GradientStop Color="#333" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Border.BorderBrush>
                                        <Border.BorderThickness>1</Border.BorderThickness>
                                        <Border.Effect>
                                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.6"/>
                                        </Border.Effect>
                                    </Border>
                                    
                                    <Track Grid.Row="1" x:Name="PART_Track">
                                        <Track.DecreaseRepeatButton>
                                            <RepeatButton Style="{StaticResource SliderTrackButton}">
                                                <RepeatButton.Background>
                                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                                        <GradientStop Color="#ff0040" Offset="0"/>
                                                        <GradientStop Color="#fa0000" Offset="0.7"/>
                                                        <GradientStop Color="#ff0040" Offset="1"/>
                                                    </LinearGradientBrush>
                                                </RepeatButton.Background>
                                                <RepeatButton.Effect>
                                                    <DropShadowEffect Color="#ff0040" Direction="0" ShadowDepth="0" BlurRadius="6" Opacity="0.8"/>
                                                </RepeatButton.Effect>
                                            </RepeatButton>
                                        </Track.DecreaseRepeatButton>
                                        <Track.Thumb>
                                            <Thumb Style="{StaticResource ModernSliderThumb}"/>
                                        </Track.Thumb>
                                        <Track.IncreaseRepeatButton>
                                            <RepeatButton Style="{StaticResource SliderTrackButton}" Background="Transparent"/>
                                        </Track.IncreaseRepeatButton>
                                    </Track>
                                </Grid>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Center-Fill Slider Style for bidirectional sliders -->
        <Style x:Key="CenterFillSlider" TargetType="Slider">
            <Setter Property="SnapsToDevicePixels" Value="true"/>
            <Setter Property="OverridesDefaultStyle" Value="true"/>
            <Setter Property="MinWidth" Value="100"/>
            <Setter Property="MinHeight" Value="40"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Slider">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            
                            <!-- Track Background -->
                            <Border Grid.Row="1" Height="6" CornerRadius="3" Margin="16,0">
                                <Border.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                        <GradientStop Color="#0a0a0a" Offset="0"/>
                                        <GradientStop Color="#1a1a1a" Offset="0.5"/>
                                        <GradientStop Color="#0a0a0a" Offset="1"/>
                                    </LinearGradientBrush>
                                </Border.Background>
                                <Border.BorderBrush>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                        <GradientStop Color="#333" Offset="0"/>
                                        <GradientStop Color="#555" Offset="0.5"/>
                                        <GradientStop Color="#333" Offset="1"/>
                                    </LinearGradientBrush>
                                </Border.BorderBrush>
                                <Border.BorderThickness>1</Border.BorderThickness>
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.6"/>
                                </Border.Effect>
                            </Border>
                            
                            <!-- Center Line (at 0) -->
                            <Border Grid.Row="1" Width="2" Height="8" Background="#888888" 
                                    HorizontalAlignment="Center" VerticalAlignment="Center" CornerRadius="1"/>
                            
                            <Track Grid.Row="1" x:Name="PART_Track" Margin="16,0">
                                <Track.DecreaseRepeatButton>
                                    <RepeatButton Style="{StaticResource SliderTrackButton}" Background="Transparent"/>
                                </Track.DecreaseRepeatButton>
                                <Track.Thumb>
                                    <Thumb Style="{StaticResource ModernSliderThumb}"/>
                                </Track.Thumb>
                                <Track.IncreaseRepeatButton>
                                    <RepeatButton Style="{StaticResource SliderTrackButton}" Background="Transparent"/>
                                </Track.IncreaseRepeatButton>
                            </Track>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    <Border Background="#222328" CornerRadius="5" Padding="12" Margin="4">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="8" BlurRadius="25" Opacity="0.3"/>
        </Border.Effect>
        <StackPanel>
            <Grid Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Border Grid.Column="0" Background="#fa0000" Width="40" Height="40" CornerRadius="20" Margin="0,0,12,0">
                    <TextBlock Text="&#xE7FC;" FontFamily="Segoe MDL2 Assets" FontSize="18" 
                               Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="Xbox Controller Automation" FontSize="18" FontWeight="SemiBold" Foreground="#e4e5e6"/>
                    <TextBlock Text="Advanced automation for Xbox controller input, including recoil control" FontSize="14" Foreground="#888888" FontStyle="Italic"/>
                </StackPanel>
            </Grid>
            
            <!-- Divider -->
            <Border Background="#333338" Height="1" Margin="0,12,0,0"/>
            
            <StackPanel x:Name="ContentArea" Margin="0,12,0,0">

                <!-- Recoil Compensation Section -->
                <Border Background="#1a1a1f" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                    <StackPanel>
                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <Border Grid.Column="0" Background="#fa0000" Width="24" Height="24" CornerRadius="4" Margin="0,0,8,0">
                                <TextBlock Text="⚡" FontSize="14" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            
                            <TextBlock Grid.Column="1" Text="Recoil Compensation" FontSize="16" FontWeight="SemiBold" Foreground="#e4e5e6" VerticalAlignment="Center"/>
                            
                            <ToggleButton Grid.Column="2" x:Name="XboxRecoilToggle" Width="40" Height="20" Background="#333338" BorderThickness="0">
                                <ToggleButton.Style>
                                    <Style TargetType="ToggleButton">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="ToggleButton">
                                                    <Border x:Name="Border" Background="#333338" CornerRadius="10" BorderThickness="1" BorderBrush="#555555">
                                                        <Ellipse x:Name="Thumb" Width="14" Height="14" Fill="#888888" HorizontalAlignment="Left" Margin="2,0,0,0"/>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsChecked" Value="True">
                                                            <Setter TargetName="Border" Property="Background" Value="#fa0000"/>
                                                            <Setter TargetName="Border" Property="BorderBrush" Value="#fa0000"/>
                                                            <Setter TargetName="Thumb" Property="Fill" Value="White"/>
                                                            <Setter TargetName="Thumb" Property="HorizontalAlignment" Value="Right"/>
                                                            <Setter TargetName="Thumb" Property="Margin" Value="0,0,2,0"/>
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </ToggleButton.Style>
                            </ToggleButton>
                        </Grid>
                        
                        <StackPanel Margin="32,0,0,0">
                            <!-- Horizontal Slider -->
                            <Grid Margin="0,0,0,16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="50"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,0,6">
                                    <TextBlock Text="Horizontal" FontSize="14" Foreground="#e4e5e6" FontWeight="Medium"/>
                                    <TextBlock Text="Controls horizontal recoil compensation" FontSize="12" Foreground="#888888" Margin="0,2,0,0" FontStyle="Italic"/>
                                </StackPanel>
                                
                                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="XboxHorizontalValue"
                                           Text="{Binding ElementName=XboxHorizontalSlider, Path=Value, StringFormat='{}{0:F0}'}" 
                                           FontSize="14" Foreground="#fa0000" FontWeight="SemiBold" 
                                           VerticalAlignment="Center" HorizontalAlignment="Right">
                                    <TextBlock.Effect>
                                        <DropShadowEffect Color="#fa0000" Direction="0" ShadowDepth="0" BlurRadius="4" Opacity="0.6"/>
                                    </TextBlock.Effect>
                                </TextBlock>
                                
                                <Slider Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" x:Name="XboxHorizontalSlider" 
                                        Minimum="-50" Maximum="50" Value="0" Style="{StaticResource CenterFillSlider}"/>
                            </Grid>
                            
                            <!-- Vertical Slider -->
                            <Grid Margin="0,0,0,16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="50"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,0,6">
                                    <TextBlock Text="Vertical" FontSize="14" Foreground="#e4e5e6" FontWeight="Medium"/>
                                    <TextBlock Text="Controls vertical recoil compensation" FontSize="12" Foreground="#888888" Margin="0,2,0,0" FontStyle="Italic"/>
                                </StackPanel>
                                
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding ElementName=XboxVerticalSlider, Path=Value, StringFormat='{}{0:F0}'}" 
                                           FontSize="14" Foreground="#fa0000" FontWeight="SemiBold" 
                                           VerticalAlignment="Center" HorizontalAlignment="Right"/>
                                
                                <Slider Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" x:Name="XboxVerticalSlider" 
                                        Minimum="-50" Maximum="50" Value="0" Style="{StaticResource CenterFillSlider}"/>
                            </Grid>
                            
                            <!-- Delay Slider -->
                            <Grid Margin="0,0,0,16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="50"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,0,6">
                                    <TextBlock Text="Delay" FontSize="14" Foreground="#e4e5e6" FontWeight="Medium"/>
                                    <TextBlock Text="Delay before compensation starts (ms)" FontSize="12" Foreground="#888888" Margin="0,2,0,0" FontStyle="Italic"/>
                                </StackPanel>
                                
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding ElementName=XboxDelaySlider, Path=Value, StringFormat='{}{0:F0} ms'}" 
                                           FontSize="14" Foreground="#fa0000" FontWeight="SemiBold" 
                                           VerticalAlignment="Center" HorizontalAlignment="Right"/>
                                
                                <Slider Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" x:Name="XboxDelaySlider" 
                                        Minimum="0" Maximum="50" Value="0" Style="{StaticResource ModernSlider}"/>
                            </Grid>
                            
                            <!-- Activation Key -->
                            <Grid Margin="0,0,0,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <StackPanel Grid.Row="0" Margin="0,0,0,8">
                                    <TextBlock Text="Activation Key" FontSize="14" Foreground="#e4e5e6" FontWeight="Medium"/>
                                    <TextBlock Text="Trigger to hold for recoil compensation" FontSize="12" Foreground="#888888" Margin="0,2,0,0" FontStyle="Italic"/>
                                </StackPanel>
                                
                                <ComboBox Grid.Row="1" x:Name="XboxActivationKeyCombo" SelectedIndex="0"
                                          Background="#1a1a1f" Foreground="#e4e5e6" BorderBrush="#333338"
                                          FontSize="12" Height="32" Padding="12,8" BorderThickness="2">
                                    <ComboBox.Style>
                                        <Style TargetType="ComboBox">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="ComboBox">
                                                        <Grid>
                                                            <ToggleButton x:Name="ToggleButton" Grid.Column="2"
                                                                          ClickMode="Press" Focusable="false"
                                                                          IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                                                          Background="{TemplateBinding Background}"
                                                                          BorderBrush="{TemplateBinding BorderBrush}"
                                                                          BorderThickness="{TemplateBinding BorderThickness}">
                                                                <ToggleButton.Style>
                                                                    <Style TargetType="ToggleButton">
                                                                        <Setter Property="Template">
                                                                            <Setter.Value>
                                                                                <ControlTemplate TargetType="ToggleButton">
                                                                                    <Border Background="{TemplateBinding Background}" 
                                                                                            BorderBrush="{TemplateBinding BorderBrush}"
                                                                                            BorderThickness="{TemplateBinding BorderThickness}"
                                                                                            CornerRadius="8">
                                                                                        <Border.Effect>
                                                                                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" BlurRadius="12" Opacity="0.4"/>
                                                                                        </Border.Effect>
                                                                                        <Grid>
                                                                                            <Grid.ColumnDefinitions>
                                                                                                <ColumnDefinition Width="*"/>
                                                                                                <ColumnDefinition Width="20"/>
                                                                                            </Grid.ColumnDefinitions>
                                                                                            <TextBlock Grid.Column="1" Text="▼" FontSize="10" 
                                                                                                       Foreground="#888888" HorizontalAlignment="Center" 
                                                                                                       VerticalAlignment="Center"/>
                                                                                        </Grid>
                                                                                    </Border>
                                                                                </ControlTemplate>
                                                                            </Setter.Value>
                                                                        </Setter>
                                                                    </Style>
                                                                </ToggleButton.Style>
                                                            </ToggleButton>
                                                            
                                                            <ContentPresenter x:Name="ContentSite" IsHitTestVisible="False"
                                                                              Content="{TemplateBinding SelectionBoxItem}"
                                                                              ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                                              ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                                                              Margin="12,0,30,0" VerticalAlignment="Center"
                                                                              HorizontalAlignment="Left"/>
                                                            
                                                            <Popup x:Name="Popup" Placement="Bottom" IsOpen="{TemplateBinding IsDropDownOpen}"
                                                                   AllowsTransparency="True" Focusable="False" PopupAnimation="Slide">
                                                                <Grid MinWidth="{TemplateBinding ActualWidth}" MaxHeight="150">
                                                                    <Border Background="#333338" BorderBrush="#fa0000" BorderThickness="1" CornerRadius="6">
                                                                        <ScrollViewer>
                                                                            <StackPanel IsItemsHost="True"/>
                                                                        </ScrollViewer>
                                                                    </Border>
                                                                </Grid>
                                                            </Popup>
                                                        </Grid>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="BorderBrush" Value="#fa0000"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                            <Setter Property="ItemContainerStyle">
                                                <Setter.Value>
                                                    <Style TargetType="ComboBoxItem">
                                                        <Setter Property="Padding" Value="12,8"/>
                                                        <Setter Property="Background" Value="Transparent"/>
                                                        <Setter Property="Foreground" Value="#e4e5e6"/>
                                                        <Setter Property="FontSize" Value="12"/>
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate TargetType="ComboBoxItem">
                                                                    <Border Background="{TemplateBinding Background}" 
                                                                            Padding="{TemplateBinding Padding}">
                                                                        <ContentPresenter/>
                                                                    </Border>
                                                                    <ControlTemplate.Triggers>
                                                                        <Trigger Property="IsMouseOver" Value="True">
                                                                            <Setter Property="Background" Value="#fa0000"/>
                                                                            <Setter Property="Foreground" Value="White"/>
                                                                        </Trigger>
                                                                    </ControlTemplate.Triggers>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </Style>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </ComboBox.Style>
                                    
                                    <ComboBoxItem Content="Left Trigger"/>
                                    <ComboBoxItem Content="Right Trigger"/>
                                    <ComboBoxItem Content="Left Bumper"/>
                                    <ComboBoxItem Content="Right Bumper"/>
                                </ComboBox>
                            </Grid>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </StackPanel>
    </Border>
</UserControl> 