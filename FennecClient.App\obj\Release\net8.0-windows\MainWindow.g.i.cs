﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "716363329719AF676A6A743B80A8F8BBB412FD39"
//------------------------------------------------------------------------------
// <auto-generated>
//     Dieser Code wurde von einem Tool generiert.
//     Laufzeitversion:4.0.30319.42000
//
//     Änderungen an dieser Datei können falsches Verhalten verursachen und gehen verloren, wenn
//     der Code erneut generiert wird.
// </auto-generated>
//------------------------------------------------------------------------------

using FennecClient.UI.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FennecClient.App {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 205 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image LogoImage;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LogoFallback;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MNKButton;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ControllerButton;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CrosshairButton;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConfigsButton;
        
        #line default
        #line hidden
        
        
        #line 320 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OverviewButton;
        
        #line default
        #line hidden
        
        
        #line 371 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton StreamproofToggle;
        
        #line default
        #line hidden
        
        
        #line 420 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ContentArea;
        
        #line default
        #line hidden
        
        
        #line 423 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MNKContent;
        
        #line default
        #line hidden
        
        
        #line 433 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ControllerContent;
        
        #line default
        #line hidden
        
        
        #line 443 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CrosshairContent;
        
        #line default
        #line hidden
        
        
        #line 454 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ConfigsContent;
        
        #line default
        #line hidden
        
        
        #line 464 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid OverviewContent;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FennecClient.App;V1.0.0.0;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 187 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TitleBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.LogoImage = ((System.Windows.Controls.Image)(target));
            return;
            case 3:
            this.LogoFallback = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.MNKButton = ((System.Windows.Controls.Button)(target));
            
            #line 224 "..\..\..\MainWindow.xaml"
            this.MNKButton.Click += new System.Windows.RoutedEventHandler(this.NavigateToTab);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ControllerButton = ((System.Windows.Controls.Button)(target));
            
            #line 248 "..\..\..\MainWindow.xaml"
            this.ControllerButton.Click += new System.Windows.RoutedEventHandler(this.NavigateToTab);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CrosshairButton = ((System.Windows.Controls.Button)(target));
            
            #line 272 "..\..\..\MainWindow.xaml"
            this.CrosshairButton.Click += new System.Windows.RoutedEventHandler(this.NavigateToTab);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ConfigsButton = ((System.Windows.Controls.Button)(target));
            
            #line 297 "..\..\..\MainWindow.xaml"
            this.ConfigsButton.Click += new System.Windows.RoutedEventHandler(this.NavigateToTab);
            
            #line default
            #line hidden
            return;
            case 8:
            this.OverviewButton = ((System.Windows.Controls.Button)(target));
            
            #line 321 "..\..\..\MainWindow.xaml"
            this.OverviewButton.Click += new System.Windows.RoutedEventHandler(this.NavigateToTab);
            
            #line default
            #line hidden
            return;
            case 9:
            this.StreamproofToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 372 "..\..\..\MainWindow.xaml"
            this.StreamproofToggle.Click += new System.Windows.RoutedEventHandler(this.StreamproofToggle_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ContentArea = ((System.Windows.Controls.Grid)(target));
            return;
            case 11:
            this.MNKContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 12:
            this.ControllerContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 13:
            this.CrosshairContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 14:
            this.ConfigsContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 15:
            this.OverviewContent = ((System.Windows.Controls.Grid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

