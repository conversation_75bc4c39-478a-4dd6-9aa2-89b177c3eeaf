{"version": 3, "targets": {"net8.0-windows7.0": {"FennecClient.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "compile": {"bin/placeholder/FennecClient.Core.dll": {}}, "runtime": {"bin/placeholder/FennecClient.Core.dll": {}}}, "FennecClient.Services/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"FennecClient.Core": "1.0.0"}, "compile": {"bin/placeholder/FennecClient.Services.dll": {}}, "runtime": {"bin/placeholder/FennecClient.Services.dll": {}}}, "FennecClient.UI/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"FennecClient.Core": "1.0.0"}, "compile": {"bin/placeholder/FennecClient.UI.dll": {}}, "runtime": {"bin/placeholder/FennecClient.UI.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App"]}}}, "libraries": {"FennecClient.Core/1.0.0": {"type": "project", "path": "../FennecClient.Core/FennecClient.Core.csproj", "msbuildProject": "../FennecClient.Core/FennecClient.Core.csproj"}, "FennecClient.Services/1.0.0": {"type": "project", "path": "../FennecClient.Services/FennecClient.Services.csproj", "msbuildProject": "../FennecClient.Services/FennecClient.Services.csproj"}, "FennecClient.UI/1.0.0": {"type": "project", "path": "../FennecClient.UI/FennecClient.UI.csproj", "msbuildProject": "../FennecClient.UI/FennecClient.UI.csproj"}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["FennecClient.Core >= 1.0.0", "FennecClient.Services >= 1.0.0", "FennecClient.UI >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.App\\FennecClient.App.csproj", "projectName": "FennecClient.App", "projectPath": "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.App\\FennecClient.App.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.App\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.Core\\FennecClient.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.Core\\FennecClient.Core.csproj"}, "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.Services\\FennecClient.Services.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.Services\\FennecClient.Services.csproj"}, "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.UI\\FennecClient.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.UI\\FennecClient.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.16, 8.0.16]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.16, 8.0.16]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.16, 8.0.16]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.16, 8.0.16]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}