<Window x:Class="FennecClient.Setup.SetupWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Fennec Client Setup" Height="400" Width="500" ResizeMode="NoResize" WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <Border Grid.Row="0" Background="#2E86AB" Padding="20">
            <StackPanel>
                <TextBlock Text="Fennec Client Setup" FontSize="20" FontWeight="Bold" Foreground="White"/>
                <TextBlock Text="Welcome to Fennec Client Installation Wizard" FontSize="12" Foreground="White" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>
        
        <StackPanel Grid.Row="1" Margin="20">
            <TextBlock Text="Installation Path:" FontWeight="Bold" Margin="0,0,0,5"/>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBox x:Name="InstallPathTextBox" Grid.Column="0" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <Button Grid.Column="1" Content="Browse..." Click="Browse_Click" Padding="10,5"/>
            </Grid>
            
            <CheckBox x:Name="CreateDesktopShortcut" Content="Create Desktop Shortcut" Margin="0,20,0,0" IsChecked="True"/>
            <CheckBox x:Name="CreateStartMenuShortcut" Content="Create Start Menu Shortcut" Margin="0,10,0,0" IsChecked="True"/>
            
            <ProgressBar x:Name="InstallProgress" Height="20" Margin="0,20,0,0" Visibility="Collapsed"/>
            <TextBlock x:Name="StatusText" Text="" Margin="0,10,0,0" Visibility="Collapsed"/>
        </StackPanel>
        
        <Border Grid.Row="2" BorderBrush="Gray" BorderThickness="0,1,0,0" Padding="20,10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button x:Name="InstallButton" Content="Install" Click="Install_Click" Padding="20,5" Margin="0,0,10,0"/>
                <Button Content="Cancel" Click="Cancel_Click" Padding="20,5"/>
            </StackPanel>
        </Border>
    </Grid>
</Window> 