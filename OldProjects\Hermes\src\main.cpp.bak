#define _CRT_SECURE_NO_WARNINGS

#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <chrono>
#include <algorithm>
#include <cstring> // Make sure string.h/cstring is included for strncpy

#include "fonts.h"
#include "config.h"  // Add the config header
#include "mouse.h"  // Add the mouse header
#include "recoil.h"  // Include the recoil header

#ifdef _WIN32
#define NOMINMAX
#include <Windows.h>
// AlphaBlend is in gdi32.lib on newer Windows SDKs
#pragma comment(lib, "gdi32.lib") 
#pragma comment(lib, "msimg32.lib") // Link to the library containing AlphaBlend

#ifndef WDA_EXCLUDEFROMCAPTURE
#define WDA_EXCLUDEFROMCAPTURE 0x00000001 // Add this in case older SDK is being used
#endif
#ifndef WDA_NONE
#define WDA_NONE 0x00000000
#endif
#include <shlobj.h> // For ITaskbarList interface
#include <shobjidl.h> // Additional COM interfaces

// This is required for the BlockInput function
#pragma comment(lib, "user32.lib")
#endif

#include <GLFW/glfw3.h>


#ifdef _WIN32
#define GLFW_EXPOSE_NATIVE_WIN32
#include <GLFW/glfw3native.h>
#endif


#include "imgui.h"
#include "backends/imgui_impl_glfw.h"
#include "backends/imgui_impl_opengl3.h"

#ifndef GLFW_TRANSPARENT_FRAMEBUFFER
#define GLFW_TRANSPARENT_FRAMEBUFFER 0x0002000A
#endif


#ifndef GLFW_SKIP_TASKBAR_HINT
#define GLFW_SKIP_TASKBAR_HINT 0x00020006
#endif

#ifndef GLFW_MOUSE_PASSTHROUGH
#define GLFW_MOUSE_PASSTHROUGH 0x0002000C
#endif

#ifndef GLFW_FLOATING
#define GLFW_FLOATING 0x00020007
#endif

#if defined(_MSC_VER) && (_MSC_VER >= 1900) && !defined(IMGUI_DISABLE_WIN32_FUNCTIONS)
#pragma comment(lib, "legacy_stdio_definitions")
#endif

// Add these includes and definitions at the top of the file with other includes
#ifdef _WIN32
#include <windows.h>
// DPI awareness definitions removed
#endif

// Forward declarations for tab rendering functions
void RenderAimAssistTab();
void RenderRecoilControlTab();
void RenderCrosshairTab();
void RenderUITab();
void RenderSettingsTab();
void RenderConfigsTab();

// Forward declarations for helper functions
const char* KeyCodeToString(int keyCode);
void DrawGradientSeparator(float spacing = 5.0f, float thickness = 1.0f);
void DrawHorizontalGradientLine(float y, float startX = 0.0f, float thickness = 1.0f);
void DrawVerticalGradientLine(float x, float startY, float endY, float thickness = 1.0f);
bool InitializeOverlay(HINSTANCE hInstance);
void ShowOverlay(bool show);
void UpdateOverlaySettings();

#ifdef _WIN32
void EnsureWindowNotInTaskbar(GLFWwindow* window);
void ApplyStreamproofSetting(GLFWwindow* window, bool enabled);
#endif

// Global variables
bool g_ApplicationRunning = true;

extern float g_AccentColor[3];
extern float g_BorderColor[3];
extern float g_TextColor[3];
extern float g_BackgroundColor[3];
extern int g_MenuTransparency;

// Tab selection variables
int g_CurrentTab = 0;
int g_KeybindsSubTab = 0;
int g_WeaponsSubTab = 0;
int g_VisualsSubTab = 0;

// Other global variables
bool g_UIVisible = true;

const int WINDOW_WIDTH = 800;
const int WINDOW_HEIGHT = 600;
const int SIDEBAR_WIDTH = 160;
const float TITLE_BAR_HEIGHT = 55.0f;

// Change from static to global (remove static keyword)
float g_AccentColor[3] = { 0.949f, 0.435f, 0.216f }; // #f26f37

// Change from static to global (remove static keyword)
float g_BackgroundColor[3] = { 0.059f, 0.094f, 0.133f }; // #0f1822
float g_BorderColor[3] = { 0.500f, 0.500f, 0.500f };
float g_TextColor[3] = { 1.000f, 1.000f, 1.000f };
int g_MenuTransparency = 60;

// Change from static to global for config saving/loading
int g_ToggleUIKey = GLFW_KEY_INSERT;
int g_BlockInputKey = GLFW_KEY_F1;
int g_RecoilPauseKey = GLFW_KEY_HOME;
static bool g_InputBlocked = false;

// Global variables for advanced options
bool g_AlwaysOnTop = true;
bool g_LockPosition = false;
bool g_EnableBackgroundAnimation = true;
int g_BackgroundAnimationTransparency = 100;

// Variables for FPS tracking - removed functionality but keeping declarations to fix build errors
bool g_ShowFpsCounter = false; // Disabled by default since functionality is removed
int g_FpsLimit = 0; // 0 means no limit
float g_AverageFPS = 0.0f;

// Add variables for the animated background
static float g_AnimationTime = 0.0f;
static float g_LastFrameTime = 0.0f;

// Change from static to global (remove static keyword)
bool g_Streamproof = true;

// Track the currently loaded config
static std::string g_CurrentLoadedConfig = "";

// Temporary fix for undefined g_ConfigSlots errors until those references are removed
static std::string g_ConfigSlots[5];

// Particle system for background effects
const int MAX_PARTICLES = 50;
enum ParticleShape { SQUARE, TRIANGLE, CIRCLE, SHAPE_COUNT };
struct Particle {
    ImVec2 position;
    ImVec2 velocity;
    float size = 0.0f;
    float alpha = 1.0f;
    float rotation = 0.0f;
    float rotationSpeed = 0.0f;
    float lifespan = 0.0f;
    float maxLifespan = 0.0f;
    ParticleShape shape = SQUARE;
    
    Particle() : position(0,0), velocity(0,0), size(0.0f), alpha(1.0f), 
                rotation(0.0f), rotationSpeed(0.0f), lifespan(0.0f), 
                maxLifespan(0.0f), shape(SQUARE) {}
};
static Particle g_Particles[MAX_PARTICLES];
static bool g_ParticlesInitialized = false;

ImFont* g_MainFont = nullptr;
ImFont* g_TitleFont = nullptr;

// Crosshair settings
bool g_EnableCrosshair = false;
int g_CrosshairType = 0; // 0 = Plus, 1 = Dot, 2 = T-shape, 3 = X
float g_CrosshairColor[3] = { 1.0f, 0.275f, 0.333f }; // Same as accent color (#ff4655)
int g_CrosshairOpacity = 100; // 0-100%
float g_CrosshairSize = 10.0f;
float g_CrosshairThickness = 1.0f;
bool g_CrosshairOutline = true;
float g_CrosshairGap = 3.0f;

// 1. Update tab order - move Settings before Configs
const char* tabNames[] = { "Weapons", "Crosshair", "Settings", "Configs" };

static int original_window_x = 0;
static int original_window_y = 0;
static bool position_stored = false;

GLFWwindow* g_Window = nullptr;

#ifdef _WIN32
HWND g_HiddenWindow = NULL;
HWND g_CodWindowHandle = NULL; // Handle to the Call of Duty window
HHOOK g_KeyboardHook = NULL;   // Global keyboard hook
HHOOK g_MouseHook = NULL;      // Global mouse hook
HWND g_OverlayWindow = NULL; // Handle to the overlay window
bool g_OverlayVisible = true; // Whether the overlay is currently visible by default
int g_OverlayOpacity = 200; // Overlay opacity (0-255)
std::wstring g_OverlayText = L""; // Empty string to remove the X
int g_OverlayFontSize = 24; // Font size for overlay text
ImVec4 g_OverlayTextColor = ImVec4(1.0f, 1.0f, 1.0f, 1.0f); // Color for overlay text
int g_OverlayPositionX = 0; // X position adjustment (percentage of screen)
int g_OverlayPositionY = 0; // Y position adjustment (percentage of screen)
int g_OverlayBorderThickness = 1; // Border thickness in pixels

#define HOTKEY_TOGGLEUI 1
#define HOTKEY_BLOCKINPUT 2

#define WM_TOGGLE_UI (WM_APP + 1)
#define WM_TOGGLE_BLOCKINPUT (WM_APP + 2)

int GlfwKeyToVk(int glfwKey)
{
    // Map GLFW key codes to Windows virtual key codes
    switch (glfwKey) {
        case GLFW_KEY_ESCAPE:        return VK_ESCAPE;
        case GLFW_KEY_ENTER:         return VK_RETURN;
        case GLFW_KEY_TAB:           return VK_TAB;
        case GLFW_KEY_BACKSPACE:     return VK_BACK;
        case GLFW_KEY_INSERT:        return VK_INSERT;
        case GLFW_KEY_DELETE:        return VK_DELETE;
        case GLFW_KEY_RIGHT:         return VK_RIGHT;
        case GLFW_KEY_LEFT:          return VK_LEFT;
        case GLFW_KEY_DOWN:          return VK_DOWN;
        case GLFW_KEY_UP:            return VK_UP;
        case GLFW_KEY_PAGE_UP:       return VK_PRIOR;
        case GLFW_KEY_PAGE_DOWN:     return VK_NEXT;
        case GLFW_KEY_HOME:          return VK_HOME;
        case GLFW_KEY_END:           return VK_END;
        case GLFW_KEY_CAPS_LOCK:     return VK_CAPITAL;
        case GLFW_KEY_SCROLL_LOCK:   return VK_SCROLL;
        case GLFW_KEY_NUM_LOCK:      return VK_NUMLOCK;
        case GLFW_KEY_PRINT_SCREEN:  return VK_SNAPSHOT;
        case GLFW_KEY_PAUSE:         return VK_PAUSE;
        case GLFW_KEY_F1:            return VK_F1;
        case GLFW_KEY_F2:            return VK_F2;
        case GLFW_KEY_F3:            return VK_F3;
        case GLFW_KEY_F4:            return VK_F4;
        case GLFW_KEY_F5:            return VK_F5;
        case GLFW_KEY_F6:            return VK_F6;
        case GLFW_KEY_F7:            return VK_F7;
        case GLFW_KEY_F8:            return VK_F8;
        case GLFW_KEY_F9:            return VK_F9;
        case GLFW_KEY_F10:           return VK_F10;
        case GLFW_KEY_F11:           return VK_F11;
        case GLFW_KEY_F12:           return VK_F12;
        // For alphabet keys, GLFW_KEY_A-Z maps to ASCII codes which are the same as VK_A-Z
        default:
            if (glfwKey >= GLFW_KEY_A && glfwKey <= GLFW_KEY_Z)
                return glfwKey; // GLFW uses ASCII which is the same as VK for A-Z
            if (glfwKey >= GLFW_KEY_0 && glfwKey <= GLFW_KEY_9)
                return glfwKey; // Same for digits 0-9
            return 0; // Unknown key
    }
}

const char* KeyCodeToString(int keyCode)
{
    switch (keyCode)
    {
        case GLFW_KEY_SPACE:         return "SPACE";
        case GLFW_KEY_APOSTROPHE:    return "APOSTROPHE";
        case GLFW_KEY_COMMA:         return "COMMA";
        case GLFW_KEY_MINUS:         return "MINUS";
        case GLFW_KEY_PERIOD:        return "PERIOD";
        case GLFW_KEY_SLASH:         return "SLASH";
        case GLFW_KEY_0:             return "0";
        case GLFW_KEY_1:             return "1";
        case GLFW_KEY_2:             return "2";
        case GLFW_KEY_3:             return "3";
        case GLFW_KEY_4:             return "4";
        case GLFW_KEY_5:             return "5";
        case GLFW_KEY_6:             return "6";
        case GLFW_KEY_7:             return "7";
        case GLFW_KEY_8:             return "8";
        case GLFW_KEY_9:             return "9";
        case GLFW_KEY_SEMICOLON:     return "SEMICOLON";
        case GLFW_KEY_EQUAL:         return "EQUAL";
        case GLFW_KEY_A:             return "A";
        case GLFW_KEY_B:             return "B";
        case GLFW_KEY_C:             return "C";
        case GLFW_KEY_D:             return "D";
        case GLFW_KEY_E:             return "E";
        case GLFW_KEY_F:             return "F";
        case GLFW_KEY_G:             return "G";
        case GLFW_KEY_H:             return "H";
        case GLFW_KEY_I:             return "I";
        case GLFW_KEY_J:             return "J";
        case GLFW_KEY_K:             return "K";
        case GLFW_KEY_L:             return "L";
        case GLFW_KEY_M:             return "M";
        case GLFW_KEY_N:             return "N";
        case GLFW_KEY_O:             return "O";
        case GLFW_KEY_P:             return "P";
        case GLFW_KEY_Q:             return "Q";
        case GLFW_KEY_R:             return "R";
        case GLFW_KEY_S:             return "S";
        case GLFW_KEY_T:             return "T";
        case GLFW_KEY_U:             return "U";
        case GLFW_KEY_V:             return "V";
        case GLFW_KEY_W:             return "W";
        case GLFW_KEY_X:             return "X";
        case GLFW_KEY_Y:             return "Y";
        case GLFW_KEY_Z:             return "Z";
        case GLFW_KEY_LEFT_BRACKET:  return "LEFT_BRACKET";
        case GLFW_KEY_BACKSLASH:     return "BACKSLASH";
        case GLFW_KEY_RIGHT_BRACKET: return "RIGHT_BRACKET";
        case GLFW_KEY_GRAVE_ACCENT:  return "GRAVE_ACCENT";
        case GLFW_KEY_ESCAPE:        return "ESCAPE";
        case GLFW_KEY_ENTER:         return "ENTER";
        case GLFW_KEY_TAB:           return "TAB";
        case GLFW_KEY_BACKSPACE:     return "BACKSPACE";
        case GLFW_KEY_INSERT:        return "INSERT";
        case GLFW_KEY_DELETE:        return "DELETE";
        case GLFW_KEY_RIGHT:         return "RIGHT";
        case GLFW_KEY_LEFT:          return "LEFT";
        case GLFW_KEY_DOWN:          return "DOWN";
        case GLFW_KEY_UP:            return "UP";
        case GLFW_KEY_PAGE_UP:       return "PAGE_UP";
        case GLFW_KEY_PAGE_DOWN:     return "PAGE_DOWN";
        case GLFW_KEY_HOME:          return "HOME";
        case GLFW_KEY_END:           return "END";
        case GLFW_KEY_CAPS_LOCK:     return "CAPS_LOCK";
        case GLFW_KEY_SCROLL_LOCK:   return "SCROLL_LOCK";
        case GLFW_KEY_NUM_LOCK:      return "NUM_LOCK";
        case GLFW_KEY_PRINT_SCREEN:  return "PRINT_SCREEN";
        case GLFW_KEY_PAUSE:         return "PAUSE";
        case GLFW_KEY_F1:            return "F1";
        case GLFW_KEY_F2:            return "F2";
        case GLFW_KEY_F3:            return "F3";
        case GLFW_KEY_F4:            return "F4";
        case GLFW_KEY_F5:            return "F5";
        case GLFW_KEY_F6:            return "F6";
        case GLFW_KEY_F7:            return "F7";
        case GLFW_KEY_F8:            return "F8";
        case GLFW_KEY_F9:            return "F9";
        case GLFW_KEY_F10:           return "F10";
        case GLFW_KEY_F11:           return "F11";
        case GLFW_KEY_F12:           return "F12";
        case GLFW_KEY_KP_0:          return "KP_0";
        case GLFW_KEY_KP_1:          return "KP_1";
        case GLFW_KEY_KP_2:          return "KP_2";
        case GLFW_KEY_KP_3:          return "KP_3";
        case GLFW_KEY_KP_4:          return "KP_4";
        case GLFW_KEY_KP_5:          return "KP_5";
        case GLFW_KEY_KP_6:          return "KP_6";
        case GLFW_KEY_KP_7:          return "KP_7";
        case GLFW_KEY_KP_8:          return "KP_8";
        case GLFW_KEY_KP_9:          return "KP_9";
        case GLFW_KEY_KP_DECIMAL:    return "KP_DECIMAL";
        case GLFW_KEY_KP_DIVIDE:     return "KP_DIVIDE";
        case GLFW_KEY_KP_MULTIPLY:   return "KP_MULTIPLY";
        case GLFW_KEY_KP_SUBTRACT:   return "KP_SUBTRACT";
        case GLFW_KEY_KP_ADD:        return "KP_ADD";
        case GLFW_KEY_KP_ENTER:      return "KP_ENTER";
        case GLFW_KEY_KP_EQUAL:      return "KP_EQUAL";
        case GLFW_KEY_LEFT_SHIFT:    return "LEFT_SHIFT";
        case GLFW_KEY_LEFT_CONTROL:  return "LEFT_CONTROL";
        case GLFW_KEY_LEFT_ALT:      return "LEFT_ALT";
        case GLFW_KEY_LEFT_SUPER:    return "LEFT_SUPER";
        case GLFW_KEY_RIGHT_SHIFT:   return "RIGHT_SHIFT";
        case GLFW_KEY_RIGHT_CONTROL: return "RIGHT_CONTROL";
        case GLFW_KEY_RIGHT_ALT:     return "RIGHT_ALT";
        case GLFW_KEY_RIGHT_SUPER:   return "RIGHT_SUPER";
        case GLFW_KEY_MENU:          return "MENU";
        default:                     return "UNKNOWN";
    }
}

// Function to find the Call of Duty window
BOOL CALLBACK FindCoDWindowProc(HWND hwnd, LPARAM lParam)
{
    char windowTitle[256];
    GetWindowTextA(hwnd, windowTitle, sizeof(windowTitle));
    
    // First try with the process name (might help in some cases)
    DWORD processId;
    GetWindowThreadProcessId(hwnd, &processId);
    
    if (processId != 0) {
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION, FALSE, processId);
        if (hProcess != NULL) {
            char processName[MAX_PATH];
            DWORD size = sizeof(processName);
            if (QueryFullProcessImageNameA(hProcess, 0, processName, &size)) {
                const char* filename = strrchr(processName, '\\');
                if (filename != NULL) {
                    filename++; // Skip the backslash
                    if (_stricmp(filename, "cod.exe") == 0 || 
                        _stricmp(filename, "BlackOps6.exe") == 0 || 
                        strstr(filename, "BlackOps") != NULL) {
                        CloseHandle(hProcess);
                        *((HWND*)lParam) = hwnd;
                        return FALSE; // Found the window, stop enumeration
                    }
                }
            }
            CloseHandle(hProcess);
        }
    }
    
    // Also check for the window title (as a fallback)
    if (strstr(windowTitle, "Call of Duty") != NULL ||
        strstr(windowTitle, "Black Ops") != NULL) {
        *((HWND*)lParam) = hwnd;
        return FALSE; // Found the window, stop enumeration
    }
    
    return TRUE; // Continue enumeration
}

// Low-level keyboard hook procedure
LRESULT CALLBACK LowLevelKeyboardProc(int nCode, WPARAM wParam, LPARAM lParam)
{
    if (nCode == HC_ACTION && g_InputBlocked) {
        KBDLLHOOKSTRUCT* pKbStruct = (KBDLLHOOKSTRUCT*)lParam;
        int vkBlockKey = GlfwKeyToVk(g_BlockInputKey);
        
        // Always allow the block toggle key to pass through so we can disable blocking
        if (pKbStruct->vkCode == vkBlockKey) {
            return CallNextHookEx(NULL, nCode, wParam, lParam);
        }
        
        // Get the foreground window
        HWND foregroundWindow = GetForegroundWindow();
        
        // If our menu is the foreground window, allow all keyboard input
        HWND ourWindowHwnd = glfwGetWin32Window(g_Window);
        if (foregroundWindow == ourWindowHwnd) {
            return CallNextHookEx(NULL, nCode, wParam, lParam);
        }
        
        // Block all keyboard input for all other applications
        return 1;
    }
    
    // Call the next hook in the chain
    return CallNextHookEx(NULL, nCode, wParam, lParam);
}

// Low-level mouse hook procedure
LRESULT CALLBACK LowLevelMouseProc(int nCode, WPARAM wParam, LPARAM lParam)
{
    if (nCode == HC_ACTION && g_InputBlocked) {
        // Get the foreground window
        HWND foregroundWindow = GetForegroundWindow();
        
        // If our menu is the foreground window, allow mouse input
        HWND ourWindowHwnd = glfwGetWin32Window(g_Window);
        if (foregroundWindow == ourWindowHwnd) {
            return CallNextHookEx(NULL, nCode, wParam, lParam);
        }
        
        // Block all mouse input for all other applications
        return 1;
    }
    
    // Call the next hook in the chain
    return CallNextHookEx(NULL, nCode, wParam, lParam);
}

// Function to toggle input blocking for all applications
void UpdateCoDInputBlocking(bool blocked)
{
    if (blocked) {
        // 1. Set up keyboard and mouse hooks if they're not already set
        if (g_KeyboardHook == NULL) {
            g_KeyboardHook = SetWindowsHookEx(WH_KEYBOARD_LL, LowLevelKeyboardProc, GetModuleHandle(NULL), 0);
            if (g_KeyboardHook == NULL) {
                printf("Failed to set keyboard hook. Error: %d\n", GetLastError());
            }
        }
        
        if (g_MouseHook == NULL) {
            g_MouseHook = SetWindowsHookEx(WH_MOUSE_LL, LowLevelMouseProc, GetModuleHandle(NULL), 0);
            if (g_MouseHook == NULL) {
                printf("Failed to set mouse hook. Error: %d\n", GetLastError());
            }
        }
        
        // 2. Try to find and specifically disable the Call of Duty window as well
        if (g_CodWindowHandle == NULL || !IsWindow(g_CodWindowHandle)) {
            g_CodWindowHandle = NULL;
            EnumWindows(FindCoDWindowProc, (LPARAM)&g_CodWindowHandle);
            
            if (g_CodWindowHandle != NULL) {
                printf("Call of Duty window found! Specifically disabling it.\n");
                // Disable the CoD window
                EnableWindow(g_CodWindowHandle, FALSE);
                // Set window to always be on bottom
                SetWindowPos(g_CodWindowHandle, HWND_BOTTOM, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE);
            }
        } else if (IsWindow(g_CodWindowHandle)) {
            // Disable the existing CoD window
            EnableWindow(g_CodWindowHandle, FALSE);
            // Set window to always be on bottom
            SetWindowPos(g_CodWindowHandle, HWND_BOTTOM, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE);
        }
        
        // 3. Ensure our menu window stays on top and focused
        if (g_Window != nullptr) {
            HWND menuHwnd = glfwGetWin32Window(g_Window);
            if (menuHwnd != NULL) {
                SetWindowPos(menuHwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);
                SetForegroundWindow(menuHwnd);
            }
        }
        
        printf("Input blocking ACTIVE - All inputs blocked except for menu\n");
    } else {
        // Remove the hooks if they exist
        if (g_KeyboardHook != NULL) {
            UnhookWindowsHookEx(g_KeyboardHook);
            g_KeyboardHook = NULL;
        }
        
        if (g_MouseHook != NULL) {
            UnhookWindowsHookEx(g_MouseHook);
            g_MouseHook = NULL;
        }
        
        // Re-enable the CoD window if we have it
        if (g_CodWindowHandle != NULL && IsWindow(g_CodWindowHandle)) {
            EnableWindow(g_CodWindowHandle, TRUE);
            SetWindowPos(g_CodWindowHandle, HWND_NOTOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);
        }
        
        // Reset our window's z-order
        if (g_Window != nullptr) {
            HWND menuHwnd = glfwGetWin32Window(g_Window);
            if (menuHwnd != NULL) {
                SetWindowPos(menuHwnd, HWND_NOTOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);
            }
        }
        
        printf("Input blocking disabled - All applications restored\n");
    }
}

LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg)
    {
        case WM_HOTKEY:
            if (wParam == HOTKEY_TOGGLEUI) {
                PostMessage(hwnd, WM_TOGGLE_UI, 0, 0);
            } else if (wParam == HOTKEY_BLOCKINPUT) {
                // Toggle input blocking when the hotkey is pressed
                g_InputBlocked = !g_InputBlocked;
                printf("Global hotkey detected: Input blocking %s\n", g_InputBlocked ? "enabled" : "disabled");
                UpdateCoDInputBlocking(g_InputBlocked);
            }
            return 0;
        
        case WM_TOGGLE_UI:
            if (g_Window) {
                g_UIVisible = !g_UIVisible;
                printf("Global hotkey detected: UI %s\n", g_UIVisible ? "shown" : "hidden");
                
                if (g_UIVisible) {
                    glfwSetWindowSize(g_Window, WINDOW_WIDTH, WINDOW_HEIGHT);
                    if (position_stored) {
                        glfwSetWindowPos(g_Window, original_window_x, original_window_y);
                    }
                    
                    glfwShowWindow(g_Window);
                    glfwFocusWindow(g_Window);
                    
                    #ifdef _WIN32
                    HWND hwnd = glfwGetWin32Window(g_Window);
                    if (hwnd) {
                        ShowWindow(hwnd, SW_SHOWNOACTIVATE);
                        SetForegroundWindow(hwnd);
                       
                        EnsureWindowNotInTaskbar(g_Window);
                    }
                    #endif
                } else {

                    if (!position_stored) {
                        glfwGetWindowPos(g_Window, &original_window_x, &original_window_y);
                        position_stored = true;
                    }
                    glfwSetWindowSize(g_Window, 1, 1);
                    glfwSetWindowPos(g_Window, -1, -1);
                }
            }
            return 0;
        
        default:
            return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
}

bool RegisterGlobalHotkey(int hotkeyId, int glfwKey)
{
    int vk = GlfwKeyToVk(glfwKey);
    if (vk == 0) {
        printf("Unsupported key for global hotkey\n");
        return false;
    }
    
    // Unregister existing hotkey if there is one
    UnregisterHotKey(g_HiddenWindow, hotkeyId);
    
    // Register the new hotkey
    if (!RegisterHotKey(g_HiddenWindow, hotkeyId, 0, vk)) {
        printf("Failed to register global hotkey. Error: %d\n", GetLastError());
        return false;
    }
    
    printf("Global hotkey registered for key: %s\n", KeyCodeToString(glfwKey));
    return true;
}

// Make sure to clean up hooks on application exit
void CleanupHooks()
{
    if (g_KeyboardHook) {
        UnhookWindowsHookEx(g_KeyboardHook);
        g_KeyboardHook = NULL;
    }
    
    if (g_MouseHook) {
        UnhookWindowsHookEx(g_MouseHook);
        g_MouseHook = NULL;
    }
    
    if (g_OverlayWindow) {
        DestroyWindow(g_OverlayWindow);
        g_OverlayWindow = NULL;
    }
    
    // ... existing code ...
}
#endif

static void glfw_error_callback(int error, const char* description)
{
    fprintf(stderr, "GLFW Error %d: %s\n", error, description);
}


void ApplyCustomTheme(bool dark, float alpha)
{
    ImGuiStyle& style = ImGui::GetStyle();
    ImVec4* colors = style.Colors;
    
    ImVec4 accentColor = ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f);
    
    ImVec4 nearBlack = ImVec4(g_BackgroundColor[0], g_BackgroundColor[1], g_BackgroundColor[2], 1.00f);
    ImVec4 darkGray = ImVec4(g_BackgroundColor[0] + 0.04f, g_BackgroundColor[1] + 0.04f, g_BackgroundColor[2] + 0.04f, 1.00f);
    ImVec4 mediumGray = ImVec4(g_BackgroundColor[0] + 0.09f, g_BackgroundColor[1] + 0.09f, g_BackgroundColor[2] + 0.09f, 1.00f);
    ImVec4 lightGray = ImVec4(g_BackgroundColor[0] + 0.13f, g_BackgroundColor[1] + 0.13f, g_BackgroundColor[2] + 0.13f, 1.00f);
    ImVec4 shadowColor = ImVec4(0.02f, 0.02f, 0.02f, 0.35f);
    
    ImVec4 textColor = ImVec4(g_TextColor[0], g_TextColor[1], g_TextColor[2], 1.00f);
    
    // Use accent color for borders with low opacity instead of using a separate border color
    ImVec4 borderColor = ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 0.30f);
    
    ImVec4 accentShadow = ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 0.15f);  
    ImVec4 accentGlow = ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 0.25f);    
    
    // Directly use slider value for transparency (0-100%)
    float actualAlpha = 1.0f - (g_MenuTransparency / 100.0f);
    
    nearBlack.w = actualAlpha;
    darkGray.w = actualAlpha;
    mediumGray.w = actualAlpha;
    lightGray.w = actualAlpha;
   

    colors[ImGuiCol_Text]                   = textColor; 
    colors[ImGuiCol_TextDisabled]           = ImVec4(textColor.x * 0.6f, textColor.y * 0.6f, textColor.z * 0.6f, 1.00f);
    colors[ImGuiCol_WindowBg]               = nearBlack;
    colors[ImGuiCol_ChildBg]                = darkGray;
    colors[ImGuiCol_PopupBg]                = nearBlack;
    colors[ImGuiCol_Border]                 = borderColor;
    colors[ImGuiCol_BorderShadow]           = ImVec4(0.00f, 0.00f, 0.00f, 0.60f);
    colors[ImGuiCol_FrameBg]                = mediumGray;
    colors[ImGuiCol_FrameBgHovered]         = lightGray;
    colors[ImGuiCol_FrameBgActive]          = ImVec4(accentColor.x, accentColor.y, accentColor.z, 0.30f);
    colors[ImGuiCol_TitleBg]                = darkGray;
    colors[ImGuiCol_TitleBgActive]          = mediumGray;
    colors[ImGuiCol_TitleBgCollapsed]       = nearBlack;
    colors[ImGuiCol_MenuBarBg]              = darkGray;
    colors[ImGuiCol_ScrollbarBg]            = ImVec4(nearBlack.x, nearBlack.y, nearBlack.z, 0.5f);
    colors[ImGuiCol_ScrollbarGrab]          = ImVec4(accentColor.x * 0.7f, accentColor.y * 0.7f, accentColor.z * 0.7f, 0.7f);
    colors[ImGuiCol_ScrollbarGrabHovered]   = accentColor;
    colors[ImGuiCol_ScrollbarGrabActive]    = ImVec4(accentColor.x, accentColor.y, accentColor.z, 1.00f);
    colors[ImGuiCol_CheckMark]              = ImVec4(accentColor.x, accentColor.y, accentColor.z, 1.00f);
    colors[ImGuiCol_SliderGrab]             = ImVec4(accentColor.x * 0.7f, accentColor.y * 0.7f, accentColor.z * 0.7f, 0.90f);
    colors[ImGuiCol_SliderGrabActive]       = ImVec4(accentColor.x, accentColor.y, accentColor.z, 1.00f);
    colors[ImGuiCol_Button]                 = ImVec4(
        g_AccentColor[0] * 0.7f,
        g_AccentColor[1] * 0.7f,
        g_AccentColor[2] * 0.7f,
        actualAlpha * 0.9f
    );
    colors[ImGuiCol_ButtonHovered]          = ImVec4(
        g_AccentColor[0],
        g_AccentColor[1],
        g_AccentColor[2],
        actualAlpha
    );
    colors[ImGuiCol_ButtonActive]           = ImVec4(
        min(g_AccentColor[0] * 1.2f, 1.0f),
        min(g_AccentColor[1] * 1.2f, 1.0f),
        min(g_AccentColor[2] * 1.2f, 1.0f),
        actualAlpha
    );
    colors[ImGuiCol_Header]                 = mediumGray;
    colors[ImGuiCol_HeaderHovered]          = lightGray;
    colors[ImGuiCol_HeaderActive]           = ImVec4(accentColor.x, accentColor.y, accentColor.z, 0.50f);
    colors[ImGuiCol_Separator]              = borderColor;
    colors[ImGuiCol_SeparatorHovered]       = ImVec4(accentColor.x, accentColor.y, accentColor.z, 0.40f);
    colors[ImGuiCol_SeparatorActive]        = ImVec4(accentColor.x, accentColor.y, accentColor.z, 0.60f);
    colors[ImGuiCol_ResizeGrip]             = ImVec4(accentColor.x, accentColor.y, accentColor.z, 0.20f); 
    colors[ImGuiCol_ResizeGripHovered]      = ImVec4(accentColor.x, accentColor.y, accentColor.z, 0.60f);
    colors[ImGuiCol_ResizeGripActive]       = ImVec4(accentColor.x, accentColor.y, accentColor.z, 0.90f);
    colors[ImGuiCol_Tab]                    = darkGray;
    colors[ImGuiCol_TabHovered]             = ImVec4(accentColor.x, accentColor.y, accentColor.z, 0.50f);
    colors[ImGuiCol_TabActive]              = ImVec4(accentColor.x, accentColor.y, accentColor.z, 0.30f);
    colors[ImGuiCol_TabUnfocused]           = darkGray;
    colors[ImGuiCol_TabUnfocusedActive]     = mediumGray;
    colors[ImGuiCol_PlotLines]              = ImVec4(accentColor.x, accentColor.y, accentColor.z, 1.00f);
    colors[ImGuiCol_PlotLinesHovered]       = ImVec4(accentColor.x, accentColor.y, accentColor.z, 1.00f);
    colors[ImGuiCol_PlotHistogram]          = ImVec4(accentColor.x, accentColor.y, accentColor.z, 1.00f);
    colors[ImGuiCol_PlotHistogramHovered]   = ImVec4(accentColor.x, accentColor.y, accentColor.z, 0.80f);
    colors[ImGuiCol_NavHighlight]           = ImVec4(accentColor.x, accentColor.y, accentColor.z, 1.00f);
    

    style.WindowPadding           = ImVec2(12, 12);
    style.FramePadding            = ImVec2(8, 6);
    style.ItemSpacing             = ImVec2(10, 8);
    style.ItemInnerSpacing        = ImVec2(6, 6);
    style.TouchExtraPadding       = ImVec2(0, 0);
    style.IndentSpacing           = 21.0f;
    style.ScrollbarSize           = 10.0f; 
    style.GrabMinSize             = 10.0f;

    style.WindowBorderSize        = 1.0f;   
    style.ChildBorderSize         = 1.0f;   
    style.PopupBorderSize         = 1.0f;
    style.FrameBorderSize         = 0.0f;   
    style.TabBorderSize           = 0.0f;

    style.WindowRounding          = 4.0f;  
    style.ChildRounding           = 4.0f;  
    style.FrameRounding           = 3.0f;  
    style.PopupRounding           = 4.0f;  
    style.ScrollbarRounding       = 4.0f;   
    style.GrabRounding            = 3.0f;  
    style.TabRounding             = 4.0f;  
    
    style.ButtonTextAlign         = ImVec2(0.5f, 0.5f); 
}


void RenderSidebar()
{
    ImGuiWindowFlags sidebar_flags = ImGuiWindowFlags_NoScrollbar;
    
    // Use consistent transparency calculation
    float alpha = 1.0f - (g_MenuTransparency / 100.0f);
    
    // Position the sidebar at the top-left corner
    ImGui::SetCursorPos(ImVec2(0, 0));
    
    // Add vertical divider at the right edge of the sidebar
    ImDrawList* drawList = ImGui::GetWindowDrawList();
    ImVec2 windowPos = ImGui::GetWindowPos();
    
    // Draw vertical divider line from top to bottom of the window
    DrawVerticalGradientLine(
        windowPos.x + SIDEBAR_WIDTH,         
        windowPos.y,                // Start from the very top              
        windowPos.y + ImGui::GetWindowHeight(),     
        1.5f                         
    );
    
    // Render main sidebar directly from the top of the menu (no separate title bar)
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0, 0));
    ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(0.075f, 0.078f, 0.09f, alpha));
    ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 0.0f); 
    ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 0.0f); 
    ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.0f, 0.0f, 0.0f, 0.0f)); 
    
    ImGui::BeginChild("SidebarContent", ImVec2(SIDEBAR_WIDTH, ImGui::GetWindowHeight()), false, sidebar_flags);
    
    ImVec2 buttonSize = ImVec2(SIDEBAR_WIDTH - 28, 40);
    
    ImVec4 selectedBgColor = ImVec4(0.0f, 0.0f, 0.0f, 0.0f);
    ImVec4 textColor = ImVec4(g_TextColor[0], g_TextColor[1], g_TextColor[2], 1.00f);
    ImVec4 accentColor = ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f);
    ImVec4 accentColorGlow = ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 0.25f); 

    ImGui::SetWindowFontScale(1.05f);
    ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(4, 2));
    
    // Small spacing at the top
    ImGui::Dummy(ImVec2(0, 10));

    // Add Hermes title centered in the sidebar content area
    if (g_TitleFont) {
        ImGui::PushFont(g_TitleFont);
        float title_width = ImGui::CalcTextSize("Hermes").x;
        ImGui::SetCursorPosX((SIDEBAR_WIDTH - title_width) * 0.5f);
        ImGui::TextColored(accentColor, "Hermes");
        ImGui::PopFont();
    } else {
        ImGui::SetWindowFontScale(1.5f);
        float title_width = ImGui::CalcTextSize("Hermes").x;
        ImGui::SetCursorPosX((SIDEBAR_WIDTH - title_width) * 0.5f);
        ImGui::TextColored(accentColor, "Hermes");
        ImGui::SetWindowFontScale(1.05f); // Reset to the current scale
    }
    
    // Add horizontal divider below the Hermes title, aligned with the titlebar divider
    ImGui::Dummy(ImVec2(0, 28.0f)); // Increase space to align with titlebar divider
    
    // Create a custom divider that matches the titlebar divider
    float dividerY = windowPos.y + TITLE_BAR_HEIGHT;
    float dividerThickness = 2.0f;
    float startX = windowPos.x;
    float endX = windowPos.x + SIDEBAR_WIDTH;
    float dividerWidth = endX - startX;
    
    // Draw gradient in three parts: faded start, solid middle, faded end
    float fadeWidth = dividerWidth * 0.25f; // Increase fade width from 15% to 25%
    float solidStart = startX + fadeWidth;
    float solidEnd = endX - fadeWidth;
    
    // Draw left fade (transparent to solid) with more pronounced transparency
    for (int i = 0; i < 20; i++) {
        float t = i / 20.0f;
        float x = startX + (t * fadeWidth);
        float nextX = startX + ((i + 1) / 20.0f) * fadeWidth;
        float alpha = t * t; // Use quadratic easing for more pronounced fade-in
        
        drawList->AddLine(
            ImVec2(x, dividerY),
            ImVec2(nextX, dividerY),
            ImGui::GetColorU32(ImVec4(accentColor.x, accentColor.y, accentColor.z, alpha * 0.8f)), // Reduce overall opacity
            dividerThickness
        );
    }
    
    // Draw solid middle (slightly reduced opacity)
    drawList->AddLine(
        ImVec2(solidStart, dividerY),
        ImVec2(solidEnd, dividerY),
        ImGui::GetColorU32(ImVec4(accentColor.x, accentColor.y, accentColor.z, 0.8f)),
        dividerThickness
    );
    
    // Draw right fade (solid to transparent) with more pronounced transparency
    for (int i = 0; i < 20; i++) {
        float t = i / 20.0f;
        float x = solidEnd + (t * fadeWidth);
        float nextX = solidEnd + ((i + 1) / 20.0f) * fadeWidth;
        float alpha = (1.0f - t) * (1.0f - t); // Use quadratic easing for more pronounced fade-out
        
        drawList->AddLine(
            ImVec2(x, dividerY),
            ImVec2(nextX, dividerY),
            ImGui::GetColorU32(ImVec4(accentColor.x, accentColor.y, accentColor.z, alpha * 0.8f)), // Reduce overall opacity
            dividerThickness
        );
    }
    
    ImGui::Dummy(ImVec2(0, 15)); // Add space after the divider
    ImGui::Indent(14.0f); // Indent for sidebar tabs
    
    // Create a consistent way to render sidebar tabs
    for (int i = 0; i < IM_ARRAYSIZE(tabNames); i++) {
        bool isSelected = (g_CurrentTab == i);
        
        // Set button styling (transparent background)
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(accentColor.x * 0.25f, accentColor.y * 0.25f, accentColor.z * 0.25f, 0.4f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(accentColor.x * 0.3f, accentColor.y * 0.3f, accentColor.z * 0.3f, 0.5f));
        ImGui::PushStyleColor(ImGuiCol_Text, isSelected ? accentColor : textColor);

        // Create a unique ID for each button to avoid any ID conflicts
        ImGui::PushID(i);

        // Render button with tab name (but with empty label to avoid duplicated text)
        // The real label will be rendered manually with the draw list
        if (ImGui::Button("##tab", buttonSize)) {
            g_CurrentTab = i;
        }

        ImGui::PopID();
        
        // Get position for indicator and text
        ImVec2 buttonMin = ImGui::GetItemRectMin();
        ImVec2 buttonMax = ImGui::GetItemRectMax();
        
        // Draw selection indicator and text
        drawList = ImGui::GetWindowDrawList();
        if (isSelected) {
            // Draw selection bar
            float barWidth = 3.0f;
            ImVec2 barStart = ImVec2(buttonMin.x - 14.0f, buttonMin.y);
            ImVec2 barEnd = ImVec2(barStart.x + barWidth, buttonMax.y);
            drawList->AddRectFilled(barStart, barEnd, ImGui::GetColorU32(accentColor));
            
            // Render text with glow effect
            ImVec2 textPos = ImVec2(buttonMin.x + 8.0f, (buttonMin.y + buttonMax.y) * 0.5f - ImGui::GetFontSize() * 0.5f);
            drawList->AddText(ImVec2(textPos.x + 1, textPos.y + 1), ImGui::GetColorU32(ImVec4(accentColor.x, accentColor.y, accentColor.z, 0.25f)), tabNames[i]);
            drawList->AddText(textPos, ImGui::GetColorU32(accentColor), tabNames[i]);
        } else {
            // Render normal text
            ImVec2 textPos = ImVec2(buttonMin.x + 8.0f, (buttonMin.y + buttonMax.y) * 0.5f - ImGui::GetFontSize() * 0.5f);
            drawList->AddText(textPos, ImGui::GetColorU32(textColor), tabNames[i]);
        }
        
        // Pop color styles
        ImGui::PopStyleColor(4);
        
        // Add spacing between tabs
        ImGui::Spacing();
    }

    ImGui::Unindent(14.0f);
    ImGui::PopStyleVar(); // ItemSpacing
    ImGui::SetWindowFontScale(1.0f);

    // Version info at the bottom
    float windowHeight = ImGui::GetWindowHeight();
    ImGui::SetCursorPosY(windowHeight - 35.0f);
    float version_width = ImGui::CalcTextSize("V1.0").x;
    ImGui::SetCursorPosX((SIDEBAR_WIDTH - version_width) * 0.5f);
    ImGui::TextColored(ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.0f), "V1.0");
    
    ImGui::EndChild();

    // Pop styles for sidebar content
    ImGui::PopStyleColor(2); // Border and ChildBg
    ImGui::PopStyleVar(3);  // WindowPadding, ChildRounding, ChildBorderSize

    ImGui::SetCursorPosX(SIDEBAR_WIDTH + 1);
}

void RenderContent()
{
    float alpha = 1.0f - (g_MenuTransparency / 200.0f);

    // Define a small margin between sidebar and titlebar to make divider visible
    const float TITLE_MARGIN_LEFT = 2.0f;
    const float titlebarStartX = SIDEBAR_WIDTH + TITLE_MARGIN_LEFT;

    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0, 0));
    ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(0.075f, 0.078f, 0.09f, alpha)); 
    ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 0.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 0.0f);
    ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.0f, 0.0f, 0.0f, 0.0f)); 

    // Move titlebar to the right with a small margin to make divider visible
    ImGui::SetCursorPos(ImVec2(titlebarStartX, 0));
    
    // Titlebar with subtabs - positioned to the right of sidebar with small margin
    ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(0.075f, 0.078f, 0.09f, alpha));
    ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 0.0f); 
    ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 0.0f);
    
    // Calculate titlebar width to be the remaining space after sidebar and margin
    float titlebarWidth = ImGui::GetWindowWidth() - titlebarStartX;
    ImGui::BeginChild("TitleBar", ImVec2(titlebarWidth, TITLE_BAR_HEIGHT), false, ImGuiWindowFlags_NoScrollbar);
    
    ImVec4 accentColor = ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f);

    // Add subtabs to titlebar if a tab is selected
    if (g_CurrentTab >= 0 && g_CurrentTab <= 1) { // Updated for Weapons and Crosshair tabs
        // Center the subtabs in the titlebar
        // We'll apply styles first so we can properly calculate widths
        
        // Apply modern, subtle tab styles with additional padding for centering
        ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(10, 4));
        ImGui::PushStyleVar(ImGuiStyleVar_ItemInnerSpacing, ImVec2(5, 4));
        ImGui::PushStyleVar(ImGuiStyleVar_TabRounding, 4.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_FrameBorderSize, 0.0f);
        
        // Force tab alignment center
        ImGui::PushStyleVar(ImGuiStyleVar_SelectableTextAlign, ImVec2(0.5f, 0.5f));
        
        // Use transparent backgrounds for all tab states
        ImGui::PushStyleColor(ImGuiCol_Tab, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
        ImGui::PushStyleColor(ImGuiCol_TabHovered, ImVec4(accentColor.x * 0.25f, accentColor.y * 0.25f, accentColor.z * 0.25f, 0.4f));
        ImGui::PushStyleColor(ImGuiCol_TabActive, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
        
        // Tab text colors - inactive uses text color, active uses accent color
        ImVec4 inactiveTabTextColor = ImVec4(g_TextColor[0] * 0.7f, g_TextColor[1] * 0.7f, g_TextColor[2] * 0.7f, 1.0f);
        ImGui::PushStyleColor(ImGuiCol_Text, inactiveTabTextColor);
        ImGui::PushStyleColor(ImGuiCol_TabUnfocused, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
        ImGui::PushStyleColor(ImGuiCol_TabUnfocusedActive, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
        
        ImGuiTabBarFlags titlebar_tab_flags = ImGuiTabBarFlags_FittingPolicyResizeDown | 
                                            ImGuiTabBarFlags_NoTooltip | 
                                            ImGuiTabBarFlags_Reorderable;
        
        // Calculate approximate width of tabs for centering
        float tabsWidth = 0.0f;
        const char* subtabNames[4] = { nullptr, nullptr, nullptr, nullptr }; // Keep size the same for compatibility
        int numSubtabs = 0;
        
        // Identify which subtabs to show based on current tab
        switch (g_CurrentTab) {
            case 0: // Weapons subtabs
                subtabNames[0] = "Aim Assist";
                subtabNames[1] = "Recoil Control";
                numSubtabs = 2;
                break;
                
            case 1: // Visuals subtabs
                subtabNames[0] = "Crosshair";
                subtabNames[1] = "UI";
                numSubtabs = 2;
                break;
        }
        
        // Estimate tabs width (20px padding per tab + text width)
        for (int i = 0; i < numSubtabs; i++) {
            if (subtabNames[i]) {
                tabsWidth += ImGui::CalcTextSize(subtabNames[i]).x + 40.0f; // 20px padding on each side
            }
        }
        
        // Center the tabs by setting the cursor position
        float centerPosX = (titlebarWidth - tabsWidth) * 0.5f;
        if (centerPosX < 10.0f) centerPosX = 10.0f; // Ensure minimum padding
        ImGui::SetCursorPosX(centerPosX);
        ImGui::SetCursorPosY((TITLE_BAR_HEIGHT - 30) * 0.5f);
                                               
        // Handle different tab types
        switch (g_CurrentTab) {
            case 0: // Weapons subtabs
                if (ImGui::BeginTabBar("TitleBarWeaponsSubTabs", titlebar_tab_flags)) {
                    // Use transparent background for active tab, only change text color
                    ImGui::PushStyleColor(ImGuiCol_TabActive, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
                    
                    // Handle each tab item without conditional style pushing/popping
                    ImGui::PushStyleColor(ImGuiCol_Text, g_WeaponsSubTab == 0 ? accentColor : inactiveTabTextColor);
                    if (ImGui::BeginTabItem("Aim Assist")) {
                        g_WeaponsSubTab = 0;
                        ImGui::EndTabItem();
                    }
                    ImGui::PopStyleColor(); // Text color for this tab
                    
                    ImGui::PushStyleColor(ImGuiCol_Text, g_WeaponsSubTab == 1 ? accentColor : inactiveTabTextColor);
                    if (ImGui::BeginTabItem("Recoil Control")) {
                        g_WeaponsSubTab = 1;
                        ImGui::EndTabItem();
                    }
                    ImGui::PopStyleColor(); // Text color for this tab
                    
                    ImGui::PopStyleColor(); // TabActive
                    ImGui::EndTabBar();
                }
            break;
                
            case 1: // Crosshair tab - no subtabs needed
            break;

            case 2: // Settings tab
            {
                ImVec2 titlePos = ImGui::GetCursorPos();
                ImGui::Dummy(ImVec2(0, 5));
                RenderSettingsTab();
                break;
            }

            case 3: // Configs tab
            {
                ImVec2 titlePos = ImGui::GetCursorPos();
                ImGui::Dummy(ImVec2(0, 5));
                RenderConfigsTab();
                break;
            }
        }
        
        ImGui::PopStyleColor(6); // Tab, TabHovered, TabActive, Text, TabUnfocused, TabUnfocusedActive
        ImGui::PopStyleVar(5); // FramePadding, ItemInnerSpacing, TabRounding, FrameBorderSize, SelectableTextAlign
    }
    
    // Add FPS counter to the titlebar if enabled
    if (g_ShowFpsCounter) {
        char fpsText[16];
        sprintf(fpsText, "%d FPS", (int)g_AverageFPS);
        float textWidth = ImGui::CalcTextSize(fpsText).x;
        ImGui::SetCursorPosX(titlebarWidth - textWidth - 15);
        ImGui::SetCursorPosY((TITLE_BAR_HEIGHT - ImGui::GetFontSize()) * 0.5f - 2.0f);
        ImGui::TextColored(accentColor, "%s", fpsText);
    }
    
    ImGui::EndChild();
    
    // Pop styles for the titlebar
    ImGui::PopStyleVar(2); // ChildRounding, ChildBorderSize
    ImGui::PopStyleColor(); // ChildBg
    
    // Add enhanced horizontal divider below titlebar - starts from titlebar's left position
    // Create a custom divider that's more visible but fades at both ends
    ImDrawList* drawList = ImGui::GetWindowDrawList();
    ImVec2 windowPos = ImGui::GetWindowPos();
    float dividerY = windowPos.y + TITLE_BAR_HEIGHT;
    float dividerThickness = 2.0f; // Increased thickness
    
    // Calculate divider start and end points
    float startX = windowPos.x + titlebarStartX;
    float endX = windowPos.x + ImGui::GetWindowWidth();
    float dividerWidth = endX - startX;
    
    // Draw gradient in three parts: faded start, solid middle, faded end
    float fadeWidth = dividerWidth * 0.15f; // 15% fade on each end
    float solidStart = startX + fadeWidth;
    float solidEnd = endX - fadeWidth;
    
    // Draw left fade (transparent to solid)
    for (int i = 0; i < 20; i++) {
        float t = i / 20.0f;
        float x = startX + (t * fadeWidth);
        float nextX = startX + ((i + 1) / 20.0f) * fadeWidth;
        float alpha = t; // Linear fade for consistent appearance
        
        drawList->AddLine(
            ImVec2(x, dividerY),
            ImVec2(nextX, dividerY),
            ImGui::GetColorU32(ImVec4(accentColor.x, accentColor.y, accentColor.z, alpha)),
            dividerThickness
        );
    }
    
    // Draw solid middle
    drawList->AddLine(
        ImVec2(solidStart, dividerY),
        ImVec2(solidEnd, dividerY),
        ImGui::GetColorU32(accentColor),
        dividerThickness
    );
    
    // Draw right fade (solid to transparent)
    for (int i = 0; i < 20; i++) {
        float t = i / 20.0f;
        float x = solidEnd + (t * fadeWidth);
        float nextX = solidEnd + ((i + 1) / 20.0f) * fadeWidth;
        float alpha = 1.0f - t; // Linear fade for consistent appearance
        
        drawList->AddLine(
            ImVec2(x, dividerY),
            ImVec2(nextX, dividerY),
            ImGui::GetColorU32(ImVec4(accentColor.x, accentColor.y, accentColor.z, alpha)),
            dividerThickness
        );
    }
    
    // Set content position with the same horizontal offset as the titlebar
    const float contentMargin = 1.0f; 
    ImVec2 contentSize = ImVec2(
        ImGui::GetWindowWidth() - titlebarStartX - (contentMargin * 2), 
        ImGui::GetWindowHeight() - TITLE_BAR_HEIGHT - (contentMargin * 2)
    );
    
    ImGui::SetCursorPos(ImVec2(titlebarStartX + contentMargin, TITLE_BAR_HEIGHT + contentMargin));

    ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
    ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 0.0f);
    
    // Only set minimum content size for tabs that need scrolling (Visuals)
    if (g_CurrentTab == 2) {
        // Set appropriate minimum height based on actual content
        ImGui::SetNextWindowContentSize(ImVec2(0.0f, 500.0f));
    }
    
    // Use standard window flags without forcing scrollbar - let ImGui show it only when needed
    ImGuiWindowFlags content_flags = ImGuiWindowFlags_AlwaysUseWindowPadding;
    ImGui::BeginChild("Content", contentSize, false, content_flags);

    ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(8, 6));
    ImGui::Dummy(ImVec2(8, 8));
    ImGui::Indent(0.0f); // Changed from 10.0f to 0.0f to start content at sidebar edge
    
    ImVec2 titlePos;

    // Modern tab style - applied only once before each tab bar begins
    switch (g_CurrentTab)
    {
        case 0: // Weapons tab
        {
            titlePos = ImGui::GetCursorPos();
            
            ImGui::Dummy(ImVec2(0, 5));
            
            // Instead of using a tab bar, render the appropriate content based on g_WeaponsSubTab
            switch (g_WeaponsSubTab)
            {
                case 0: // Aim Assist
                    RenderAimAssistTab();
                    break;
                case 1: // Recoil Control
                    RenderRecoilControlTab();
                    break;
            }
            
            break;
        }
        
        case 1: // Crosshair tab
        {
            titlePos = ImGui::GetCursorPos();
            ImGui::Dummy(ImVec2(0, 5));
            RenderCrosshairTab();
            break;
        }

        case 2: // Settings tab
        {
            titlePos = ImGui::GetCursorPos();
            ImGui::Dummy(ImVec2(0, 5));
            RenderSettingsTab();
            break;
        }

        case 3: // Configs tab
        {
            titlePos = ImGui::GetCursorPos();
            ImGui::Dummy(ImVec2(0, 5));
            RenderConfigsTab();
            break;
        }
    }
    
    ImGui::PopStyleVar(); // ItemSpacing
    ImGui::EndChild();

    // Pop styles for content area
    ImGui::PopStyleVar(); // ChildBorderSize
    ImGui::PopStyleColor(); // Border
    
    // Pop remaining styles from the beginning of RenderContent
    ImGui::PopStyleColor(2); // ChildBg, Border
    ImGui::PopStyleVar(3); // WindowPadding, ChildRounding, ChildBorderSize
}

bool CaptureKeyPress(int* keyPtr)
{
    bool capturing = ImGui::IsItemActive();
    if (capturing)
    {
        ImGui::TextColored(ImVec4(1.0f, 0.3f, 0.3f, 1.0f), "Press any key...");
        
        for (int i = 0; i < 12; i++)
            if (ImGui::IsKeyPressed((ImGuiKey)(ImGuiKey_F1 + i))) { *keyPtr = GLFW_KEY_F1 + i; return false; }
        
        for (int i = 0; i < 26; i++)
            if (ImGui::IsKeyPressed((ImGuiKey)(ImGuiKey_A + i))) { *keyPtr = GLFW_KEY_A + i; return false; }
        for (int i = 0; i < 10; i++)
            if (ImGui::IsKeyPressed((ImGuiKey)(ImGuiKey_0 + i))) { *keyPtr = GLFW_KEY_0 + i; return false; }
            
        if (ImGui::IsKeyPressed(ImGuiKey_Tab))        { *keyPtr = GLFW_KEY_TAB; return false; }
        if (ImGui::IsKeyPressed(ImGuiKey_Insert))     { *keyPtr = GLFW_KEY_INSERT; return false; }
        if (ImGui::IsKeyPressed(ImGuiKey_Delete))     { *keyPtr = GLFW_KEY_DELETE; return false; }
        if (ImGui::IsKeyPressed(ImGuiKey_Home))       { *keyPtr = GLFW_KEY_HOME; return false; }
        if (ImGui::IsKeyPressed(ImGuiKey_End))        { *keyPtr = GLFW_KEY_END; return false; }
        if (ImGui::IsKeyPressed(ImGuiKey_PageUp))     { *keyPtr = GLFW_KEY_PAGE_UP; return false; }
        if (ImGui::IsKeyPressed(ImGuiKey_PageDown))   { *keyPtr = GLFW_KEY_PAGE_DOWN; return false; }
    }
    return capturing;
}


void key_callback(GLFWwindow* window, int key, int scancode, int action, int mods)
{
    // Only process UI toggle key
    if (key == g_ToggleUIKey && action == GLFW_PRESS) {
        g_UIVisible = !g_UIVisible;
        printf("UI toggle detected in callback: %s\n", g_UIVisible ? "shown" : "hidden");
        
        GLFWerrorfun originalErrorCallback = glfwSetErrorCallback(NULL);
        
        if (g_UIVisible) {
            glfwSetWindowSize(window, WINDOW_WIDTH, WINDOW_HEIGHT);
            if (position_stored) {
                glfwSetWindowPos(window, original_window_x, original_window_y);
            }
            glfwFocusWindow(window);
            
            #ifdef _WIN32
            HWND hwnd = glfwGetWin32Window(window);
            if (hwnd) {
                ShowWindow(hwnd, SW_SHOWNOACTIVATE);
                SetForegroundWindow(hwnd);
                
                EnsureWindowNotInTaskbar(window);
            }
            #endif
        } else {
            if (!position_stored) {
                glfwGetWindowPos(window, &original_window_x, &original_window_y);
                position_stored = true;
            }
            
            glfwSetWindowSize(window, 1, 1);
            glfwSetWindowPos(window, -1, -1);
        }
        
        glfwSetErrorCallback(originalErrorCallback);
        
        #ifdef _WIN32
        if (g_HiddenWindow) {
            RegisterGlobalHotkey(HOTKEY_TOGGLEUI, g_ToggleUIKey);
        }
        #endif
    }
}

// Initialize a single particle with random properties
void InitializeParticle(Particle& particle, bool randomPosition = true) {
    // Random position if requested, otherwise use current position
    if (randomPosition) {
        particle.position.x = static_cast<float>(rand() % WINDOW_WIDTH);
        particle.position.y = static_cast<float>(rand() % WINDOW_HEIGHT);
    }
    
    // Random velocity (slow moving)
    float angle = static_cast<float>(rand() % 360) * 3.14159f / 180.0f;
    float speed = 5.0f + static_cast<float>(rand() % 20) / 10.0f;
    particle.velocity.x = cosf(angle) * speed;
    particle.velocity.y = sinf(angle) * speed;
    
    // Random size (small)
    particle.size = 2.0f + static_cast<float>(rand() % 8);
    
    // Random alpha (mostly transparent)
    particle.alpha = 0.05f + static_cast<float>(rand() % 15) / 100.0f;
    
    // Random rotation and rotation speed
    particle.rotation = static_cast<float>(rand() % 360) * 3.14159f / 180.0f;
    particle.rotationSpeed = (static_cast<float>(rand() % 20) - 10.0f) / 400.0f;
    
    // Random shape
    particle.shape = static_cast<ParticleShape>(rand() % SHAPE_COUNT);
    
    // Random lifespan
    particle.maxLifespan = 5.0f + static_cast<float>(rand() % 10);
    particle.lifespan = particle.maxLifespan;
}

// Initialize all particles
void InitializeParticles() {
    for (int i = 0; i < MAX_PARTICLES; i++) {
        InitializeParticle(g_Particles[i]);
    }
    g_ParticlesInitialized = true;
}

// Update and render particles
void UpdateAndRenderParticles(float deltaTime) {
    // Don't render if animation is disabled
    if (!g_EnableBackgroundAnimation) return;
    
    // Initialize particles if needed
    if (!g_ParticlesInitialized) {
        InitializeParticles();
    }
    
    // ALWAYS use the background draw list - this renders behind everything else
    // and is not affected by the menu transparency
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    
    for (int i = 0; i < MAX_PARTICLES; i++) {
        Particle& particle = g_Particles[i];
        
        // Update position
        particle.position.x += particle.velocity.x * deltaTime;
        particle.position.y += particle.velocity.y * deltaTime;
        
        // Update rotation
        particle.rotation += particle.rotationSpeed * deltaTime;
        
        // Update lifespan
        particle.lifespan -= deltaTime;
        
        // Respawn if needed
        if (particle.lifespan <= 0 || 
            particle.position.x < -50 || particle.position.x > WINDOW_WIDTH + 50 ||
            particle.position.y < -50 || particle.position.y > WINDOW_HEIGHT + 50) {
            InitializeParticle(particle);
        }
        
        // Set a much higher fixed alpha to ensure particles are always visible
        // This alpha is completely independent of menu transparency
        float alpha = 0.5f + (particle.alpha * 0.5f);
        
        // Draw particle using accent color with some variation but higher brightness
        float hueShift = static_cast<float>(i % 3) * 0.2f;
        ImVec4 particleColor = ImVec4(
            g_AccentColor[0] * (1.0f + hueShift * 0.5f), 
            g_AccentColor[1] * (1.0f + hueShift * 0.5f), 
            g_AccentColor[2] * (1.0f + hueShift * 0.5f), 
            alpha
        );
        
        ImVec2 center = particle.position;
        
        // Draw different shapes based on particle type
        switch (particle.shape) {
            case SQUARE: {
                // Draw rotated square
                float halfSize = particle.size / 2.0f;
                float s = sinf(particle.rotation);
                float c = cosf(particle.rotation);
                
                ImVec2 points[4];
                points[0] = ImVec2(center.x + (c * -halfSize - s * -halfSize), center.y + (s * -halfSize + c * -halfSize));
                points[1] = ImVec2(center.x + (c * halfSize - s * -halfSize), center.y + (s * halfSize + c * -halfSize));
                points[2] = ImVec2(center.x + (c * halfSize - s * halfSize), center.y + (s * halfSize + c * halfSize));
                points[3] = ImVec2(center.x + (c * -halfSize - s * halfSize), center.y + (s * -halfSize + c * halfSize));
                
                drawList->AddConvexPolyFilled(points, 4, ImGui::ColorConvertFloat4ToU32(particleColor));
                break;
            }
            case TRIANGLE: {
                // Draw rotated triangle
                float size = particle.size * 1.2f; // Make triangles slightly larger
                float s = sinf(particle.rotation);
                float c = cosf(particle.rotation);
                
                ImVec2 points[3];
                points[0] = ImVec2(center.x + c * 0 - s * -size, center.y + s * 0 + c * -size);
                points[1] = ImVec2(center.x + c * (size * 0.866f) - s * (size * 0.5f), center.y + s * (size * 0.866f) + c * (size * 0.5f));
                points[2] = ImVec2(center.x + c * (-size * 0.866f) - s * (size * 0.5f), center.y + s * (-size * 0.866f) + c * (size * 0.5f));
                
                drawList->AddConvexPolyFilled(points, 3, ImGui::ColorConvertFloat4ToU32(particleColor));
                break;
            }
            case CIRCLE: {
                // Draw circle
                drawList->AddCircleFilled(center, particle.size, ImGui::ColorConvertFloat4ToU32(particleColor), 16);
                break;
            }
        }
    }
}

// Function to render animated background effects when input blocking is active
void RenderBlockingAnimation(float deltaTime)
{
    if (!g_InputBlocked) return;
    
    ImDrawList* draw_list = ImGui::GetBackgroundDrawList();
    
    // Update animation time
    g_AnimationTime += deltaTime;
    
    // Window dimensions
    ImVec2 windowPos = ImVec2(0, 0);
    ImVec2 windowSize = ImVec2(WINDOW_WIDTH, WINDOW_HEIGHT);
    
    // Get accent color with pulsing alpha
    float pulse = (sin(g_AnimationTime * 3.0f) + 1.0f) * 0.5f;
    float glowStrength = 0.3f + 0.7f * pulse;
    
    // Create outer glow effect
    ImVec4 glowColor = ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], glowStrength * 0.7f);
    ImU32 glowColorU32 = ImGui::GetColorU32(glowColor);
    
    // Draw animated border - thickness varies with time
    float borderThickness = 3.0f + (pulse * 2.0f);
    
    // Draw border with varying thickness
    draw_list->AddRect(
        windowPos,
        ImVec2(windowPos.x + windowSize.x, windowPos.y + windowSize.y),
        glowColorU32,
        6.0f,                           // Corner rounding
        ImDrawFlags_None,               // No extra flags
        borderThickness                 // Thickness
    );
    
    // Draw corner indicators - pulsing circles at each corner
    float cornerSize = 8.0f + (pulse * 6.0f);
    
    // Top-left corner
    draw_list->AddCircleFilled(
        ImVec2(windowPos.x + 8.0f, windowPos.y + 8.0f),
        cornerSize,
        glowColorU32
    );
    
    // Top-right corner
    draw_list->AddCircleFilled(
        ImVec2(windowPos.x + windowSize.x - 8.0f, windowPos.y + 8.0f),
        cornerSize,
        glowColorU32
    );
    
    // Bottom-left corner
    draw_list->AddCircleFilled(
        ImVec2(windowPos.x + 8.0f, windowPos.y + windowSize.y - 8.0f),
        cornerSize,
        glowColorU32
    );
    
    // Bottom-right corner
    draw_list->AddCircleFilled(
        ImVec2(windowPos.x + windowSize.x - 8.0f, windowPos.y + windowSize.y - 8.0f),
        cornerSize,
        glowColorU32
    );
    
    // Draw text indicator that blocking is active
    float textPulse = (sin(g_AnimationTime * 2.0f) + 1.0f) * 0.5f;
    ImVec4 textColor = ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 0.5f + (textPulse * 0.5f));
    
    // Draw text at the bottom of the window
    ImFont* font = ImGui::GetFont();
    const char* blockText = "INPUT BLOCKING ACTIVE";
    ImVec2 textSize = font->CalcTextSizeA(font->FontSize * 1.2f, FLT_MAX, 0.0f, blockText);
    
    draw_list->AddText(
        ImVec2(
            windowPos.x + (windowSize.x - textSize.x) * 0.5f,
            windowPos.y + windowSize.y - 30.0f
        ),
        ImGui::GetColorU32(textColor),
        blockText
    );
}

int main(int, char**)
{
    // DPI awareness disabled (removed to improve performance)

    glfwSetErrorCallback(glfw_error_callback);
    if (!glfwInit())
        return 1;


    int major, minor, revision;
    glfwGetVersion(&major, &minor, &revision);
    

    printf("GLFW Version: %d.%d.%d\n", major, minor, revision);
    

    bool transparentFramebufferSupported = (major > 3 || (major == 3 && minor >= 3));
    

    const char* glsl_version = "#version 130";
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
    glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);
    
    glfwWindowHint(GLFW_RESIZABLE, GLFW_FALSE);
    glfwWindowHint(GLFW_DECORATED, GLFW_FALSE);  
    glfwWindowHint(GLFW_SKIP_TASKBAR_HINT, GLFW_TRUE);  
    glfwWindowHint(GLFW_TRANSPARENT_FRAMEBUFFER, GLFW_TRUE);  // Enable transparent framebuffer for menu transparency
    glfwWindowHint(GLFW_MOUSE_PASSTHROUGH, GLFW_FALSE);  
    glfwWindowHint(GLFW_FLOATING, GLFW_TRUE);
    
    GLFWwindow* window = glfwCreateWindow(WINDOW_WIDTH, WINDOW_HEIGHT, "Hermes", nullptr, nullptr);
    if (window == nullptr)
        return 1;
    
    g_Window = window;

    // Initialize mouse to Xbox controller conversion
    if (!MouseToXbox::Initialize()) {
        std::cerr << "Failed to initialize MouseToXbox conversion" << std::endl;
        // Continue anyway as this is not a critical feature
    }

    glfwMakeContextCurrent(window);
    glfwSwapInterval(0); // Disable VSync to allow higher framerates
    
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;   
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableGamepad; 
    
    io.Fonts->AddFontDefault(); 
    
    float fontSize = 16.0f;
    ImFontConfig fontConfig;
    fontConfig.SizePixels = fontSize;
    fontConfig.OversampleH = 2;
    fontConfig.OversampleV = 1;
    fontConfig.PixelSnapH = true;
    
    // Load fonts from memory instead of from file
    g_MainFont = LoadHitmarkerMediumFont(io.Fonts, fontSize);
    
    ImFontConfig titleFontConfig;
    titleFontConfig.SizePixels = 24.0f; 
    titleFontConfig.OversampleH = 2;
    titleFontConfig.OversampleV = 1;
    titleFontConfig.PixelSnapH = true;
    
    g_TitleFont = LoadHitmarkerBoldFont(io.Fonts, 24.0f);
    
    // If loading from embedded data fails, add a fallback
    if (!g_TitleFont) {
        printf("Failed to load embedded Bold font, using Medium as fallback\n");
        g_TitleFont = g_MainFont;
    }
    
    if (!g_MainFont) {
        printf("Failed to load embedded fonts, using system default\n");
        
        // Try system fonts as fallback
        const char* fontOptions[] = {
            "C:\\Windows\\Fonts\\calibri.ttf",    
            "C:\\Windows\\Fonts\\tahoma.ttf",    
            "C:\\Windows\\Fonts\\arial.ttf"   
        };
        
        for (int i = 0; i < sizeof(fontOptions) / sizeof(fontOptions[0]); i++) {
            g_MainFont = io.Fonts->AddFontFromFileTTF(fontOptions[i], fontSize, &fontConfig);
            if (g_MainFont) {
                printf("Successfully loaded fallback font: %s\n", fontOptions[i]);
                break;
            }
        }
    } else {
        printf("Successfully loaded embedded Hitmarker fonts!\n");
    }
    
    if (g_MainFont) {
        io.FontDefault = g_MainFont;
    } else {
        printf("Could not load any custom font, using default\n");
    }
    

    ApplyCustomTheme(false, 1.0f);
    

    ImGui_ImplGlfw_InitForOpenGL(window, true);
    ImGui_ImplOpenGL3_Init(glsl_version);
    

    ImVec4 clear_color = ImVec4(0.05f, 0.05f, 0.05f, 1.00f);
    

    int initial_x, initial_y;
    glfwGetWindowPos(window, &initial_x, &initial_y);
    

    bool isDragging = false;
    double dragStartX = 0.0, dragStartY = 0.0; // Initialize to avoid warnings
    

    bool transparencyErrorShown = false;



    if (transparentFramebufferSupported) {
        GLFWerrorfun originalErrorCallback = glfwSetErrorCallback(NULL);
        glfwSetWindowAttrib(window, GLFW_MOUSE_PASSTHROUGH, GLFW_FALSE);
        glfwSetWindowAttrib(window, GLFW_FOCUS_ON_SHOW, GLFW_TRUE);
        glfwSetErrorCallback(originalErrorCallback);
        printf("Mouse interaction enabled at startup\n");
    }

    glfwSetKeyCallback(window, key_callback);

    #ifdef _WIN32
    if (window && g_Streamproof)
    {
        ApplyStreamproofSetting(window, true);
    }

    EnsureWindowNotInTaskbar(window);

    WNDCLASS wc = {0};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = GetModuleHandle(NULL);
    wc.lpszClassName = L"HermesHotkeyWindow";

    if (RegisterClass(&wc))
    {
        g_HiddenWindow = CreateWindow(
            L"HermesHotkeyWindow", L"Hermes Hotkey Window",
            0, 0, 0, 0, 0, HWND_MESSAGE, NULL, GetModuleHandle(NULL), NULL
        );
        
        if (g_HiddenWindow)
        {
            printf("Hidden window created for global hotkeys\n");
            RegisterGlobalHotkey(HOTKEY_TOGGLEUI, g_ToggleUIKey);
        }
        else
        {
            printf("Failed to create hidden window\n");
        }
    }
    else
    {
        printf("Failed to register window class\n");
    }
    #endif

    // Initialize the overlay window
    if (InitializeOverlay(GetModuleHandle(NULL))) {
        printf("Overlay window initialized successfully\n");
        // Show the overlay immediately to test if it works
        g_OverlayVisible = true;
        ShowOverlay(true);
    } else {
        printf("Failed to initialize overlay window\n");
    }

    while (!glfwWindowShouldClose(window) && g_ApplicationRunning)
    {
        // Store the time for animations
        double frameStartTime = glfwGetTime();
        
        // Calculate delta time for animations
        float currentTime = (float)glfwGetTime();
        float deltaTime = currentTime - g_LastFrameTime;
        g_LastFrameTime = currentTime;
        
        glfwPollEvents();
        
        // Update MouseToXbox controller emulation
        MouseToXbox::Update();
        
        #ifdef _WIN32
        if (g_HiddenWindow)
        {
            MSG msg;
            while (PeekMessage(&msg, g_HiddenWindow, 0, 0, PM_REMOVE))
            {
                TranslateMessage(&msg);
                DispatchMessage(&msg);
            }
        }
        
        // Process overlay window messages
        if (g_OverlayWindow && g_OverlayVisible)
        {
            MSG msg;
            while (PeekMessage(&msg, g_OverlayWindow, 0, 0, PM_REMOVE))
            {
                TranslateMessage(&msg);
                DispatchMessage(&msg);
            }
        }
        #endif

        ImGui_ImplOpenGL3_NewFrame();
        ImGui_ImplGlfw_NewFrame();
        ImGui::NewFrame();
        
        // Update and render background particles
        UpdateAndRenderParticles(deltaTime);
        
        static bool keyWasPressed = false;

        if (ImGui::IsKeyPressed(ImGuiKey_Escape))
            g_ApplicationRunning = false;

        static bool isDraggingWindow = false;
        
        if (ImGui::IsMouseClicked(0))
        {
            double mouseX, mouseY;
            glfwGetCursorPos(window, &mouseX, &mouseY);
            
            float headerHeight = 55.0f;

            if (mouseY <= headerHeight && !g_LockPosition)
            {
                isDraggingWindow = true;
                glfwGetCursorPos(window, &dragStartX, &dragStartY);
            }
        }
        
        if (isDraggingWindow && ImGui::IsMouseDown(0) && !g_LockPosition)
        {
            double currentX, currentY;
            glfwGetCursorPos(window, &currentX, &currentY);
            
            int windowX, windowY;
            glfwGetWindowPos(window, &windowX, &windowY);
            
            int newX = windowX + static_cast<int>(currentX - dragStartX);
            int newY = windowY + static_cast<int>(currentY - dragStartY);
            
            // Constrain window position within screen boundaries
            int screenWidth, screenHeight;
            glfwGetMonitorWorkarea(glfwGetPrimaryMonitor(), nullptr, nullptr, &screenWidth, &screenHeight);
            
            if (newX < 0) newX = 0;
            if (newX > screenWidth - WINDOW_WIDTH) newX = screenWidth - WINDOW_WIDTH;
            if (newY < 0) newY = 0;
            if (newY > screenHeight - WINDOW_HEIGHT) newY = screenHeight - WINDOW_HEIGHT;
            
            glfwSetWindowPos(window, newX, newY);
        }
        
        if (ImGui::IsMouseReleased(0))
        {
            isDraggingWindow = false;
        }


        if (g_UIVisible)
        {

            if (transparentFramebufferSupported) {
                GLFWerrorfun originalErrorCallback = glfwSetErrorCallback(NULL);
                glfwSetWindowAttrib(window, GLFW_TRANSPARENT_FRAMEBUFFER, GLFW_FALSE);
                glfwSetWindowAttrib(window, GLFW_FLOATING, GLFW_TRUE);
                glfwSetErrorCallback(originalErrorCallback);
            }


            ImGuiWindowFlags window_flags = 
                ImGuiWindowFlags_NoTitleBar | 
                ImGuiWindowFlags_NoResize | 
                ImGuiWindowFlags_NoCollapse | 
                ImGuiWindowFlags_NoScrollbar | 
                ImGuiWindowFlags_NoScrollWithMouse |
                ImGuiWindowFlags_NoBringToFrontOnFocus;
            

            ImGui::SetNextWindowPos(ImVec2(0, 0));
            ImGui::SetNextWindowSize(ImVec2(WINDOW_WIDTH, WINDOW_HEIGHT));
            

            ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 0.0f));
            ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0.0f);
            ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0, 0));
            
            ImGui::Begin("Main Window", nullptr, window_flags);

            // Correctly apply menu transparency with a more effective range
            float alpha = 1.0f - (g_MenuTransparency / 100.0f);

            // Let the RenderSidebar and RenderContent functions handle the UI layout
            RenderSidebar();
            RenderContent();
            
            ImGui::End(); 
            
            // Make sure all pushed styles are popped
            ImGui::PopStyleVar(2); // Pop WindowPadding and WindowBorderSize
            ImGui::PopStyleColor(); // Pop Border
        }
        
        // FPS counter is now displayed in the titlebar, so we don't need a separate window

        ImGui::Render();
        
        int display_w, display_h;
        glfwGetFramebufferSize(window, &display_w, &display_h);
        glViewport(0, 0, display_w, display_h);
        

        if (g_UIVisible) {
            // Use a more direct calculation for actual alpha transparency
            float actualAlpha = 1.0f - (g_MenuTransparency / 100.0f);
            glClearColor(clear_color.x, clear_color.y, clear_color.z, actualAlpha);
        } else {

            if (transparentFramebufferSupported && !transparencyErrorShown) {
                GLFWerrorfun originalErrorCallback = glfwSetErrorCallback(NULL);

                glfwSetWindowAttrib(window, GLFW_TRANSPARENT_FRAMEBUFFER, GLFW_TRUE);
                
                glfwSetWindowAttrib(window, GLFW_FLOATING, GLFW_TRUE);
               
                glfwSetErrorCallback(originalErrorCallback);

                if (glfwGetError(NULL) != GLFW_NO_ERROR) {
                    transparencyErrorShown = true;
                }
            }
        }
        
        glClear(GL_COLOR_BUFFER_BIT);
        ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
        
        glfwSwapBuffers(window);
        
        // High precision FPS limiter for high refresh rates
        if (g_FpsLimit > 0) {
            // Calculate how long this frame took
            double frameEndTime = glfwGetTime();
            double frameDuration = frameEndTime - frameStartTime;
            
            // Calculate target frame duration in seconds
            double targetDuration = 1.0 / g_FpsLimit;
            
            // If frame completed too quickly, we need to wait
            if (frameDuration < targetDuration) {
                double sleepTime = targetDuration - frameDuration;
                
                #ifdef _WIN32
                // Use QueryPerformanceCounter for high precision timing
                LARGE_INTEGER frequency, startCount, currentCount;
                QueryPerformanceFrequency(&frequency);
                QueryPerformanceCounter(&startCount);
                
                // Convert sleep time to QPC units
                LONGLONG countTarget = startCount.QuadPart + (LONGLONG)(sleepTime * frequency.QuadPart);
                
                // For longer waits, sleep first to save CPU
                if (sleepTime > 0.002) { // Only use Sleep for waits longer than 2ms
                    // Use 80% of the time in Sleep
                    double sleepPortion = sleepTime * 0.8;
                    Sleep((DWORD)(sleepPortion * 1000));
                }
                
                // Spin for the remainder of the time for precision
                do {
                    QueryPerformanceCounter(&currentCount);
                    // Add a _mm_pause() to reduce CPU usage during spinning
                    #ifdef _M_IX86
                    _mm_pause();
                    #elif defined(_M_X64)
                    _mm_pause();
                    #endif
                } while (currentCount.QuadPart < countTarget);
                #else
                // For non-Windows systems, use chrono
                auto start = std::chrono::high_resolution_clock::now();
                auto end = start + std::chrono::duration<double>(sleepTime);
                
                // Sleep for most of the time
                if (sleepTime > 0.002) {
                    std::this_thread::sleep_for(std::chrono::duration<double>(sleepTime * 0.8));
                }
                
                // Spin for the remainder
                while (std::chrono::high_resolution_clock::now() < end) {
                    // Just spin
                }
                #endif
            }
        }
    }
    
    // Call cleanup function to release all resources
    CleanupHooks();
    
    // Clean up ImGui resources
    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplGlfw_Shutdown();
    ImGui::DestroyContext();
    
    #ifdef _WIN32
    if (g_HiddenWindow)
    {
        UnregisterHotKey(g_HiddenWindow, HOTKEY_TOGGLEUI);
        UnregisterHotKey(g_HiddenWindow, HOTKEY_BLOCKINPUT);
        DestroyWindow(g_HiddenWindow);
    }
    #endif
    
    // Clean up MouseToXbox
    MouseToXbox::Cleanup();
    
    // Cleanup GLFW
    glfwDestroyWindow(window);
    glfwTerminate();
    
    return 0;
} 

// Tab rendering functions
void RenderAimAssistTab()
{
    ImGui::Dummy(ImVec2(0, 5));
    ImGui::Text("Aim assist settings will be implemented here.");
}

void RenderRecoilControlTab()
{
    ImGui::Dummy(ImVec2(0, 3));
    
    float contentWidth = ImGui::GetContentRegionAvail().x;
    float controlWidth = 200.0f;
    
    // Apply consistent text color to all text
    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(g_TextColor[0], g_TextColor[1], g_TextColor[2], 1.0f));
    
    // Reference to the recoil control system
    RecoilControl& recoilControl = RecoilControl::getInstance();
    
    // Enable recoil control - master switch
    ImGui::TextColored(ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f), "Recoil Control");
    ImGui::Dummy(ImVec2(0, 3));
    
    // Style for interactive elements
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(g_AccentColor[0]*0.3f, g_AccentColor[1]*0.3f, g_AccentColor[2]*0.3f, 0.6f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(g_AccentColor[0]*0.4f, g_AccentColor[1]*0.4f, g_AccentColor[2]*0.4f, 0.7f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(g_AccentColor[0]*0.5f, g_AccentColor[1]*0.5f, g_AccentColor[2]*0.5f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(g_TextColor[0], g_TextColor[1], g_TextColor[2], 1.0f));
    ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 0.9f));
    ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.0f));
    ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(g_AccentColor[0]*0.3f, g_AccentColor[1]*0.3f, g_AccentColor[2]*0.3f, 0.6f));
    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(g_AccentColor[0]*0.4f, g_AccentColor[1]*0.4f, g_AccentColor[2]*0.4f, 0.7f));
    ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(g_AccentColor[0]*0.5f, g_AccentColor[1]*0.5f, g_AccentColor[2]*0.5f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_PopupBg, ImVec4(g_BackgroundColor[0], g_BackgroundColor[1], g_BackgroundColor[2], 0.95f));
    
    // Enable recoil control checkbox
    static bool recoilEnabled = recoilControl.isEnabled();
    ImGui::Text("Enable Recoil Control");
    ImGui::SameLine(contentWidth - controlWidth);
    if (ImGui::Checkbox("##EnableRecoil", &recoilEnabled)) {
        recoilControl.setEnabled(recoilEnabled);
    }
    
    // Add a separator
    DrawGradientSeparator(5.0f, 1.0f);
    
    // Input Device Controls
    ImGui::TextColored(ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f), "Activation Controls");
    ImGui::Dummy(ImVec2(0, 3));
    
    // Mouse activation button
    static int mouseActivation = 0; // 0 = Left Button, 1 = Right Button
    ImGui::Text("Mouse Activation");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    
    const char* mouseButtons[] = { "Left Button", "Right Button" };
    const int mouseButtonValues[] = { VK_LBUTTON, VK_RBUTTON };
    
    // Find current mouse button
    for (int i = 0; i < IM_ARRAYSIZE(mouseButtonValues); i++) {
        if (mouseButtonValues[i] == recoilControl.getActivationKey()) {
            mouseActivation = i;
            break;
        }
    }
    
    if (ImGui::Combo("##MouseButton", &mouseActivation, mouseButtons, IM_ARRAYSIZE(mouseButtons))) {
        recoilControl.setActivationKey(mouseButtonValues[mouseActivation]);
    }
    
    // Controller activation trigger
    static int controllerActivation = 0; // 0 = Left Trigger, 1 = Right Trigger
    ImGui::Text("Controller Activation");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    
    const char* triggers[] = { "Left Trigger (LT)", "Right Trigger (RT)" };
    
    if (ImGui::Combo("##ControllerTrigger", &controllerActivation, triggers, IM_ARRAYSIZE(triggers))) {
        // Controller trigger selection is handled elsewhere in the code
    }
    
    // Add info text about hold mode
    ImGui::TextColored(ImVec4(g_TextColor[0], g_TextColor[1], g_TextColor[2], 0.7f), "Hold button/trigger to activate recoil control");
    
    // Add a separator
    DrawGradientSeparator(5.0f, 1.0f);
    
    // Recoil compensation controls
    ImGui::TextColored(ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f), "Recoil Compensation");
    ImGui::Dummy(ImVec2(0, 3));
    
    // Vertical recoil control (-50 to 50)
    static int verticalRecoil = recoilControl.getVerticalRecoil();
    ImGui::Text("Vertical Recoil");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    if (ImGui::SliderInt("##VerticalRecoil", &verticalRecoil, -50, 50)) {
        recoilControl.setVerticalRecoil(verticalRecoil);
    }
    
    if (ImGui::IsItemHovered()) {
        ImGui::SetTooltip("Negative values move cursor up, positive values move cursor down");
    }
    
    // Horizontal recoil control (-50 to 50)
    static int horizontalRecoil = recoilControl.getHorizontalRecoil();
    ImGui::Text("Horizontal Recoil");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    if (ImGui::SliderInt("##HorizontalRecoil", &horizontalRecoil, -50, 50)) {
        recoilControl.setHorizontalRecoil(horizontalRecoil);
    }
    
    if (ImGui::IsItemHovered()) {
        ImGui::SetTooltip("Negative values move cursor left, positive values move cursor right");
    }
    
    // Add a separator
    DrawGradientSeparator(5.0f, 1.0f);
    
    // Advanced controls
    ImGui::TextColored(ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f), "Advanced Settings");
    ImGui::Dummy(ImVec2(0, 3));
    
    // Smoothness setting (0.0 - 1.0)
    static float smoothness = recoilControl.getSmoothness();
    ImGui::Text("Smoothness");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    if (ImGui::SliderFloat("##Smoothness", &smoothness, 0.0f, 1.0f, "%.2f")) {
        recoilControl.setSmoothness(smoothness);
    }
    
    if (ImGui::IsItemHovered()) {
        ImGui::SetTooltip("Higher values make recoil correction more gradual");
    }
    
    // Randomization setting (0.0 - 1.0)
    static float randomization = recoilControl.getRandomization();
    ImGui::Text("Randomization");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    if (ImGui::SliderFloat("##Randomization", &randomization, 0.0f, 1.0f, "%.2f")) {
        recoilControl.setRandomization(randomization);
    }
    
    if (ImGui::IsItemHovered()) {
        ImGui::SetTooltip("Higher values add more random variation to recoil correction");
    }
    
    ImGui::PopStyleColor(10); // Pop all style colors
    ImGui::PopStyleColor(1); // Pop text color
}

void RenderCrosshairTab()
{
    ImGui::Dummy(ImVec2(0, 3));
    
    float contentWidth = ImGui::GetContentRegionAvail().x;
    float controlWidth = 200.0f;
    
    // Apply consistent text color to all text
    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(g_TextColor[0], g_TextColor[1], g_TextColor[2], 1.0f));
    
    // Crosshair Enabling
    ImGui::TextColored(ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f), "Crosshair Settings");
    ImGui::Dummy(ImVec2(0, 3));
    
    // Style for interactive elements
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(g_AccentColor[0]*0.3f, g_AccentColor[1]*0.3f, g_AccentColor[2]*0.3f, 0.6f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(g_AccentColor[0]*0.4f, g_AccentColor[1]*0.4f, g_AccentColor[2]*0.4f, 0.7f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(g_AccentColor[0]*0.5f, g_AccentColor[1]*0.5f, g_AccentColor[2]*0.5f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(g_TextColor[0], g_TextColor[1], g_TextColor[2], 1.0f));
    ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 0.9f));
    ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.0f));
    ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(g_AccentColor[0]*0.3f, g_AccentColor[1]*0.3f, g_AccentColor[2]*0.3f, 0.6f));
    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(g_AccentColor[0]*0.4f, g_AccentColor[1]*0.4f, g_AccentColor[2]*0.4f, 0.7f));
    ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(g_AccentColor[0]*0.5f, g_AccentColor[1]*0.5f, g_AccentColor[2]*0.5f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_PopupBg, ImVec4(g_BackgroundColor[0], g_BackgroundColor[1], g_BackgroundColor[2], 0.95f));
    
    // Enable crosshair toggle
    ImGui::Text("Enable Crosshair");
    ImGui::SameLine(contentWidth - controlWidth);
    if (ImGui::Checkbox("##EnableCrosshair", &g_EnableCrosshair)) {
        UpdateOverlaySettings();
    }
    
    // Show crosshair options always (removed the conditional)
    // Crosshair type combo
    ImGui::Text("Crosshair Type");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    
    const char* crosshairTypes[] = { "+", "Dot", "x" };
    bool typeChanged = ImGui::Combo("##CrosshairType", &g_CrosshairType, crosshairTypes, IM_ARRAYSIZE(crosshairTypes));
    if (typeChanged) {
        // Just update the overlay without disabling/enabling the crosshair
        UpdateOverlaySettings();
    }
    
    // Crosshair size slider
    ImGui::Text("Size");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    if (ImGui::SliderFloat("##CrosshairSize", &g_CrosshairSize, 2.0f, 30.0f, "%.1f")) {
        UpdateOverlaySettings();
    }
    
    // Crosshair thickness slider
    ImGui::Text("Thickness");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    if (ImGui::SliderFloat("##CrosshairThickness", &g_CrosshairThickness, 1.0f, 5.0f, "%.1f")) {
        UpdateOverlaySettings();
    }
    
    // Crosshair gap slider (only for Plus, T, and X shapes)
    if (g_CrosshairType != 1) { // Not for Dot type
        ImGui::Text("Gap");
        ImGui::SameLine(contentWidth - controlWidth);
        ImGui::SetNextItemWidth(controlWidth);
        if (ImGui::SliderFloat("##CrosshairGap", &g_CrosshairGap, 0.0f, 10.0f, "%.1f")) {
            UpdateOverlaySettings();
        }
    }
    
    // Crosshair outline toggle
    ImGui::Text("Outline");
    ImGui::SameLine(contentWidth - controlWidth);
    if (ImGui::Checkbox("##CrosshairOutline", &g_CrosshairOutline)) {
        UpdateOverlaySettings();
    }
    
    // Crosshair color picker
    ImGui::Text("Color");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    if (ImGui::ColorEdit3("##CrosshairColor", g_CrosshairColor, ImGuiColorEditFlags_DisplayRGB | ImGuiColorEditFlags_InputRGB)) {
        UpdateOverlaySettings();
    }
    
    ImGui::PopStyleColor(10); // Pop all the style colors
    ImGui::PopStyleColor(1); // Pop text color
}

void RenderUITab()
{
    ImGui::Dummy(ImVec2(0, 3));
    
    float contentWidth = ImGui::GetContentRegionAvail().x;
    float controlWidth = 200.0f;
    
    // Apply consistent text color to all text
    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(g_TextColor[0], g_TextColor[1], g_TextColor[2], 1.0f));
    
    // Display settings
    ImGui::TextColored(ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f), "Display Settings");
    ImGui::Dummy(ImVec2(0, 3));
    
    // Style for interactive elements
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(g_AccentColor[0]*0.3f, g_AccentColor[1]*0.3f, g_AccentColor[2]*0.3f, 0.6f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(g_AccentColor[0]*0.4f, g_AccentColor[1]*0.4f, g_AccentColor[2]*0.4f, 0.7f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(g_AccentColor[0]*0.5f, g_AccentColor[1]*0.5f, g_AccentColor[2]*0.5f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(g_TextColor[0], g_TextColor[1], g_TextColor[2], 1.0f));
    ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 0.9f));
    ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.0f));
    
    // FPS counter toggle
    // FPS counter and limiter controls removed for performance
    
    
    // Background animation toggle
    ImGui::Text("Background Animation");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::Checkbox("##BackgroundAnimation", &g_EnableBackgroundAnimation);
    
    DrawGradientSeparator(1.0f, 1.0f);
    
    // Window settings
    ImGui::TextColored(ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f), "Window Settings");
    ImGui::Dummy(ImVec2(0, 3));
    
    // Replace "Always on Top" with "Streamproof"
    ImGui::Text("Streamproof");
    ImGui::SameLine(contentWidth - controlWidth);
    bool oldStreamproofValue = g_Streamproof;
    if (ImGui::Checkbox("##Streamproof", &g_Streamproof)) {
        if (g_Window) {
            ApplyStreamproofSetting(g_Window, g_Streamproof);
        }
        // Also update overlay window when streamproof setting changes
        if (g_OverlayWindow) {
            SetWindowDisplayAffinity(g_OverlayWindow, g_Streamproof ? WDA_EXCLUDEFROMCAPTURE : WDA_NONE);
        }
    }
    
    if (ImGui::IsItemHovered()) {
        ImGui::SetTooltip("Makes the app invisible to screen capture software");
    }
    
    // Lock position toggle with aligned checkbox
    ImGui::Text("Lock Window Position");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::Checkbox("##LockWindowPosition", &g_LockPosition);
    
    DrawGradientSeparator(1.0f, 1.0f);
    
    // Appearance settings
    ImGui::TextColored(ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f), "Appearance Settings");
    ImGui::Dummy(ImVec2(0, 3));
    
    // Menu transparency with aligned slider
    ImGui::Text("Menu Transparency");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    ImGui::SliderInt("##MenuTransparency", &g_MenuTransparency, 0, 100, "%d%%");
    
    // Set style for color pickers
    ImGui::PushStyleVar(ImGuiStyleVar_FrameBorderSize, 0.0f);
    
    // Store previous accent color to detect changes
    float prev_accent_color[3] = { g_AccentColor[0], g_AccentColor[1], g_AccentColor[2] };
    float prev_text_color[3] = { g_TextColor[0], g_TextColor[1], g_TextColor[2] };
    
    // Accent color with aligned picker
    ImGui::Text("Accent Color");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    bool accent_changed = ImGui::ColorEdit3("##AccentColor", g_AccentColor, ImGuiColorEditFlags_DisplayRGB | ImGuiColorEditFlags_InputRGB);
    
    // Text color with aligned picker
    ImGui::Text("Text Color");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    bool text_changed = ImGui::ColorEdit3("##TextColor", g_TextColor, ImGuiColorEditFlags_DisplayRGB | ImGuiColorEditFlags_InputRGB);
    
    // If colors changed, immediately reapply the theme
    if (accent_changed || text_changed) {
        ApplyCustomTheme(true, 1.0f);
        // Also update overlay to reflect accent color changes
        UpdateOverlaySettings();
    }
    
    ImGui::PopStyleVar();
    
    // Removed keybinds section
    
    ImGui::PopStyleColor(6); // Pop interactive elements
    ImGui::PopStyleColor(1); // Pop text color
}

void RenderUIKeybindsTab()
{
    ImGui::Dummy(ImVec2(0, 5));
    
    float contentWidth = ImGui::GetContentRegionAvail().x;
    float controlWidth = 200.0f;
    
    // Apply consistent text color to all text
    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(g_TextColor[0], g_TextColor[1], g_TextColor[2], 1.0f));
    
    // UI Keybinds settings
    ImGui::TextColored(ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f), "Application Control Keybinds");
    ImGui::Dummy(ImVec2(0, 5));
    
    // Style for dropdowns
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(g_AccentColor[0]*0.3f, g_AccentColor[1]*0.3f, g_AccentColor[2]*0.3f, 0.6f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(g_AccentColor[0]*0.4f, g_AccentColor[1]*0.4f, g_AccentColor[2]*0.4f, 0.7f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(g_AccentColor[0]*0.5f, g_AccentColor[1]*0.5f, g_AccentColor[2]*0.5f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_PopupBg, ImVec4(g_BackgroundColor[0], g_BackgroundColor[1], g_BackgroundColor[2], 0.95f));
    ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 0.5f));
    
    // Toggle UI key dropdown
    ImGui::Text("Toggle UI Key");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    
    // Define toggle UI key options
    const char* toggleKeys[] = { "Insert", "Home", "Page Up", "Page Down", "Delete", "End" };
    int toggleKeyValues[] = { GLFW_KEY_INSERT, GLFW_KEY_HOME, GLFW_KEY_PAGE_UP, GLFW_KEY_PAGE_DOWN, GLFW_KEY_DELETE, GLFW_KEY_END };
    
    // Find the current toggle key in our list
    static int currentToggleKeyIndex = 0;
    for (int i = 0; i < IM_ARRAYSIZE(toggleKeyValues); i++) {
        if (toggleKeyValues[i] == g_ToggleUIKey) {
            currentToggleKeyIndex = i;
            break;
        }
    }
    
    // Create the dropdown
    if (ImGui::Combo("##ToggleUIKey", &currentToggleKeyIndex, toggleKeys, IM_ARRAYSIZE(toggleKeys))) {
        g_ToggleUIKey = toggleKeyValues[currentToggleKeyIndex];
    }
    
    ImGui::Spacing();
    
    // Block input key dropdown
    ImGui::Text("Block Input Key");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    
    // Define block input key options
    const char* blockKeys[] = { "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12" };
    int blockKeyValues[] = { GLFW_KEY_F1, GLFW_KEY_F2, GLFW_KEY_F3, GLFW_KEY_F4, GLFW_KEY_F5, GLFW_KEY_F6, 
                           GLFW_KEY_F7, GLFW_KEY_F8, GLFW_KEY_F9, GLFW_KEY_F10, GLFW_KEY_F11, GLFW_KEY_F12 };
    
    // Find the current block key in our list
    static int currentBlockKeyIndex = 0;
    for (int i = 0; i < IM_ARRAYSIZE(blockKeyValues); i++) {
        if (blockKeyValues[i] == g_BlockInputKey) {
            currentBlockKeyIndex = i;
            break;
        }
    }
    
    // Create the dropdown
    if (ImGui::Combo("##BlockInputKey", &currentBlockKeyIndex, blockKeys, IM_ARRAYSIZE(blockKeys))) {
        g_BlockInputKey = blockKeyValues[currentBlockKeyIndex];
    }
    
    ImGui::PopStyleColor(5); // Pop frame and popup styles
    ImGui::PopStyleColor(1); // Pop text color
}

// Add a new function to render the Configs tab
void RenderConfigsTab()
{
    ImGui::Dummy(ImVec2(0, 5));
    ImGui::TextColored(ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.0f), "Configuration Management");
    ImGui::Spacing();
    
    static char configNameBuffer[64] = "";
    static std::vector<std::string> configFiles;
    static bool refreshList = true;
    
    // Auto-refresh the config list
    if (refreshList) {
        configFiles = GetConfigFiles();
        std::sort(configFiles.begin(), configFiles.end());
        refreshList = false;
    }
    
    // Get content width for proper alignment
    float contentWidth = ImGui::GetContentRegionAvail().x;
    float buttonWidth = 120.0f;
    float inputWidth = 250.0f;
    
    // Config name input with aligned save button
    ImGui::AlignTextToFramePadding();
    ImGui::Text("Config Name:"); 
    ImGui::SameLine();
    
    // Position elements to align with the right side
    float saveButtonPosX = contentWidth - buttonWidth;
    float inputPosX = saveButtonPosX - inputWidth - 10.0f;
    
    // Input field positioned to the left of the Save button
    ImGui::SameLine(inputPosX);
    ImGui::SetNextItemWidth(inputWidth);
    ImGui::InputText("##ConfigName", configNameBuffer, sizeof(configNameBuffer));
    
    // Save button aligned to the right
    ImGui::SameLine(saveButtonPosX);
    
    // Check if we're overwriting or reaching the limit
    bool isOverwriting = false;
    for (const auto& config : configFiles) {
        if (config == configNameBuffer) {
            isOverwriting = true;
            break;
        }
    }
    
    // Disable save button if we have 10+ configs and not overwriting
    if (configFiles.size() >= 10 && !isOverwriting && configNameBuffer[0] != '\0') {
        ImGui::BeginDisabled();
        ImGui::Button("Save Config", ImVec2(buttonWidth, 0));
        ImGui::EndDisabled();
        
        if (ImGui::IsItemHovered()) {
            ImGui::SetTooltip("Maximum 10 configs. Delete one first or overwrite existing.");
        }
    } else if (configNameBuffer[0] != '\0') {
        // Regular save button when limit not reached or overwriting
        if (ImGui::Button("Save Config", ImVec2(buttonWidth, 0))) {
            SaveConfig(configNameBuffer);
            refreshList = true;
        }
    } else {
        // Disabled when no name entered
        ImGui::BeginDisabled();
        ImGui::Button("Save Config", ImVec2(buttonWidth, 0));
        ImGui::EndDisabled();
    }
    
    // Replace the two spacing calls with a gradient separator
    DrawGradientSeparator(5.0f, 1.0f);
    
    // Display config counter
    ImGui::TextColored(ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.0f), 
                      "Available Configurations (%d/10)", (int)configFiles.size());
    ImGui::Spacing();
    
    // Basic list of configs with right-aligned buttons
    for (size_t i = 0; i < configFiles.size(); i++) {
        ImGui::PushID((int)i);
        
        // Config name on left
        ImGui::AlignTextToFramePadding();
        ImGui::Text("%s", configFiles[i].c_str());
        
        // Load button aligned right
        ImGui::SameLine(saveButtonPosX - buttonWidth - 10.0f);
        if (ImGui::Button("Load", ImVec2(buttonWidth, 0))) {
            LoadConfig(configFiles[i]);
            strncpy(configNameBuffer, configFiles[i].c_str(), sizeof(configNameBuffer) - 1);
            configNameBuffer[sizeof(configNameBuffer) - 1] = '\0';
        }
        
        // Delete button also aligned right - delete immediately without confirmation
        ImGui::SameLine(saveButtonPosX);
        if (ImGui::Button("Delete", ImVec2(buttonWidth, 0))) {
            DeleteConfig(configFiles[i]);
            
            // Clear the input field if it matches the deleted config
            if (strcmp(configNameBuffer, configFiles[i].c_str()) == 0) {
                configNameBuffer[0] = '\0';
            }
            
            refreshList = true;
        }
        
        ImGui::PopID();
    }
}

void DrawVerticalGradientLine(float x, float startY, float endY, float thickness)
{
    ImDrawList* draw_list = ImGui::GetWindowDrawList();
    
    // Get current accent color to ensure consistency
    ImVec4 accentColor(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.0f);
    
    const float lineHeight = endY - startY;
    const int segments = 10;
    const float segmentHeight = lineHeight / segments;
    
    for (int i = 0; i < segments; i++) {
        float y1 = startY + (i * segmentHeight);
        float y2 = startY + ((i + 1) * segmentHeight);
        
        // Reverse the alpha calculation to make it transparent at bottom, visible at top
        float alpha1 = 1.0f - (i / float(segments - 1));
        float alpha2 = 1.0f - ((i + 1) / float(segments - 1));
        
        ImVec4 col1 = ImVec4(accentColor.x, accentColor.y, accentColor.z, alpha1);
        ImVec4 col2 = ImVec4(accentColor.x, accentColor.y, accentColor.z, alpha2);
        
        draw_list->AddRectFilledMultiColor(
            ImVec2(x - thickness/2, y1),
            ImVec2(x + thickness/2, y2),
            ImGui::ColorConvertFloat4ToU32(col1),
            ImGui::ColorConvertFloat4ToU32(col1),
            ImGui::ColorConvertFloat4ToU32(col2),
            ImGui::ColorConvertFloat4ToU32(col2)
        );
    }
}

void DrawGradientSeparator(float spacing, float thickness)
{
    ImDrawList* draw_list = ImGui::GetWindowDrawList();
    
    if (spacing > 0)
        ImGui::Dummy(ImVec2(0, spacing));
    
    ImVec2 p = ImGui::GetCursorScreenPos();
    
    float width = ImGui::GetContentRegionAvail().x;
    
    ImVec4 accentColor = ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f);
    ImU32 accentColorU32 = ImGui::GetColorU32(accentColor);
    
    float y = p.y + thickness / 2.0f;
    
    // Adjust gradient - now only fades on the right side
    float gradientWidth = width * 0.4f;  // Gradient width on right side
    float solidWidth = width - gradientWidth; // Solid part on left side
    
    // Draw solid part from left edge to where gradient begins
    draw_list->AddLine(
        ImVec2(p.x, y),
        ImVec2(p.x + solidWidth, y),
        accentColorU32,
        thickness
    );
    
    // Draw gradient on right side only
    const int segments = 30;
    for (int i = 0; i < segments; i++) {
        float t = i / (float)segments;
        float x = p.x + solidWidth + t * gradientWidth;
        
        // Calculate alpha - fades out from left to right
        float alpha = 1.0f - (t * t * (3.0f - 2.0f * t));
        
        ImU32 color = ImGui::GetColorU32(ImVec4(accentColor.x, accentColor.y, accentColor.z, alpha));
        
        draw_list->AddLine(
            ImVec2(x, y),
            ImVec2(x + gradientWidth/segments, y),
            color,
            thickness
        );
    }
    
    ImGui::Dummy(ImVec2(0, thickness));
    
    if (spacing > 0)
        ImGui::Dummy(ImVec2(0, spacing));
}

#ifdef _WIN32
void EnsureWindowNotInTaskbar(GLFWwindow* window)
{
    if (!window) return;
    
#ifdef _WIN32
    HWND hwnd = glfwGetWin32Window(window);
    if (!hwnd) return;
    
    LONG_PTR style = GetWindowLongPtr(hwnd, GWL_EXSTYLE);
    
    // Add WS_EX_TOOLWINDOW to hide from taskbar 
    // Add WS_EX_NOACTIVATE to prevent window from stealing focus
    style |= WS_EX_TOOLWINDOW | WS_EX_NOACTIVATE;
    
    SetWindowLongPtr(hwnd, GWL_EXSTYLE, style);
    
    // Also try to use the ITaskbarList interface to explicitly remove from taskbar
    HRESULT hr = CoInitialize(NULL);
    if (SUCCEEDED(hr)) {
        ITaskbarList* pTaskbarList = NULL;
        hr = CoCreateInstance(CLSID_TaskbarList, NULL, CLSCTX_INPROC_SERVER, 
                            IID_ITaskbarList, (void**)&pTaskbarList);
        
        if (SUCCEEDED(hr) && pTaskbarList) {
            pTaskbarList->HrInit();
            pTaskbarList->DeleteTab(hwnd);
            pTaskbarList->Release();
        }
        
        CoUninitialize();
    }
    
    printf("Applied WS_EX_TOOLWINDOW and WS_EX_NOACTIVATE styles to hide from taskbar\n");
#endif
}

void ApplyStreamproofSetting(GLFWwindow* window, bool enabled)
{
    if (!window) return;
    
#ifdef _WIN32
    HWND hwnd = glfwGetWin32Window(window);
    if (hwnd)
    {
        DWORD affinity = enabled ? WDA_EXCLUDEFROMCAPTURE : WDA_NONE;
        // Use SUCCEEDED macro to check for success
        if (SUCCEEDED(SetWindowDisplayAffinity(hwnd, affinity)))
        {
            printf("Applied streamproof setting: %s\n", enabled ? "enabled" : "disabled");
        }
        else
        {
            printf("Failed to apply streamproof setting\n");
        }
    }
#else
    (void)enabled;
#endif
}
#endif

void DrawHorizontalGradientLine(float y, float startX, float thickness)
{
    ImDrawList* draw_list = ImGui::GetWindowDrawList();
    
    // Get current accent color to ensure consistency
    ImVec4 accentColor(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.0f);
    
    // Calculate window width to determine end point of gradient
    float windowWidth = ImGui::GetContentRegionAvail().x;
    float endX = startX + windowWidth;
    
    // We want the line to be visible on the left (first 100 pixels) and fade to transparent
    float visibleWidth = 100.0f;  // First 100 pixels will be visible
    
    const int segments = 10;
    const float segmentWidth = visibleWidth / segments;
    
    // Get current cursor position to convert y (relative) to screen coordinates
    ImVec2 cursorScreenPos = ImGui::GetCursorScreenPos();
    float screenY = cursorScreenPos.y + y;
    float screenStartX = cursorScreenPos.x + startX;
    
    for (int i = 0; i < segments; i++) {
        float x1 = screenStartX + (i * segmentWidth);
        float x2 = screenStartX + ((i + 1) * segmentWidth);
        
        // Calculate alpha to fade out from left (visible) to right (transparent)
        float alpha1 = 1.0f - (i / float(segments - 1));
        float alpha2 = 1.0f - ((i + 1) / float(segments - 1));
        
        ImVec4 col1 = ImVec4(accentColor.x, accentColor.y, accentColor.z, alpha1);
        ImVec4 col2 = ImVec4(accentColor.x, accentColor.y, accentColor.z, alpha2);
        
        draw_list->AddRectFilledMultiColor(
            ImVec2(x1, screenY - thickness/2),
            ImVec2(x2, screenY + thickness/2),
            ImGui::ColorConvertFloat4ToU32(col1),
            ImGui::ColorConvertFloat4ToU32(col2),
            ImGui::ColorConvertFloat4ToU32(col2),
            ImGui::ColorConvertFloat4ToU32(col1)
        );
    }
}

LRESULT CALLBACK OverlayWindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg)
    {
        case WM_DESTROY:
            g_OverlayVisible = false;
            return 0;

        case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);
            
            // Set text properties for complete transparency
            SetBkMode(hdc, TRANSPARENT);
            
            // Get window dimensions
            RECT rect;
            GetClientRect(hwnd, &rect);
            int width = rect.right - rect.left;
            int height = rect.bottom - rect.top;
            
            // Clear the entire window with black (our transparent color key)
            HBRUSH blackBrush = CreateSolidBrush(RGB(0, 0, 0));
            FillRect(hdc, &rect, blackBrush);
            
            // Create an off-screen DC for compositing with alpha
            HDC hdcMem = CreateCompatibleDC(hdc);
            HBITMAP hBitmap = CreateCompatibleBitmap(hdc, width, height);
            HBITMAP hOldBitmap = (HBITMAP)SelectObject(hdcMem, hBitmap);
            
            // Fill memory DC with black (transparent color)
            FillRect(hdcMem, &rect, blackBrush);
            SetBkMode(hdcMem, TRANSPARENT);

            // Only draw crosshair if enabled
            if (g_EnableCrosshair) {
                // Calculate center position
                int centerX = width / 2 + (width * g_OverlayPositionX) / 100;
                int centerY = height / 2 + (height * g_OverlayPositionY) / 100;
                
                // Setup crosshair color with opacity
                COLORREF crosshairColor = RGB(
                    (int)(g_CrosshairColor[0] * 255),
                    (int)(g_CrosshairColor[1] * 255),
                    (int)(g_CrosshairColor[2] * 255)
                );
                
                // Setup outline color (black)
                COLORREF outlineColor = RGB(0, 0, 0);
                
                // Use full opacity instead of user setting
                BYTE alpha = 255; // Full opacity
                
                // Convert sizes from float to int
                int size = (int)g_CrosshairSize;
                int thickness = max(1, (int)g_CrosshairThickness); // Ensure minimum thickness
                int gap = (int)g_CrosshairGap;
                
                // Create pens for drawing
                HPEN crosshairPen = CreatePen(PS_SOLID, thickness, crosshairColor);
                HPEN outlinePen = NULL;
                if (g_CrosshairOutline) {
                    outlinePen = CreatePen(PS_SOLID, thickness + 2, outlineColor);
                }
                
                // Create brushes for filled shapes
                HBRUSH crosshairBrush = CreateSolidBrush(crosshairColor);
                HBRUSH outlineBrush = NULL;
                if (g_CrosshairOutline) {
                    outlineBrush = CreateSolidBrush(outlineColor);
                }
                
                // Draw crosshair based on type
                switch (g_CrosshairType) {
                    case 0: // Plus (+)
                        // Draw outline first if enabled
                        if (g_CrosshairOutline && outlinePen != NULL) {
                            SelectObject(hdcMem, outlinePen);
                            // Horizontal line
                            MoveToEx(hdcMem, centerX - size, centerY, NULL);
                            LineTo(hdcMem, centerX - gap, centerY);
                            MoveToEx(hdcMem, centerX + gap, centerY, NULL);
                            LineTo(hdcMem, centerX + size, centerY);
                            
                            // Vertical line
                            MoveToEx(hdcMem, centerX, centerY - size, NULL);
                            LineTo(hdcMem, centerX, centerY - gap);
                            MoveToEx(hdcMem, centerX, centerY + gap, NULL);
                            LineTo(hdcMem, centerX, centerY + size);
                        }
                        
                        // Draw main crosshair
                        SelectObject(hdcMem, crosshairPen);
                        // Horizontal line
                        MoveToEx(hdcMem, centerX - size, centerY, NULL);
                        LineTo(hdcMem, centerX - gap, centerY);
                        MoveToEx(hdcMem, centerX + gap, centerY, NULL);
                        LineTo(hdcMem, centerX + size, centerY);
                        
                        // Vertical line
                        MoveToEx(hdcMem, centerX, centerY - size, NULL);
                        LineTo(hdcMem, centerX, centerY - gap);
                        MoveToEx(hdcMem, centerX, centerY + gap, NULL);
                        LineTo(hdcMem, centerX, centerY + size);
                        break;
                        
                    case 1: // Dot
                        // Draw outline first if enabled
                        if (g_CrosshairOutline && outlineBrush != NULL) {
                            SelectObject(hdcMem, outlineBrush);
                            Ellipse(hdcMem, centerX - size/2 - 1, centerY - size/2 - 1, 
                                  centerX + size/2 + 1, centerY + size/2 + 1);
                        }
                        
                        // Draw main dot
                        SelectObject(hdcMem, crosshairBrush);
                        Ellipse(hdcMem, centerX - size/2, centerY - size/2, 
                              centerX + size/2, centerY + size/2);
                        break;
                        
                    case 2: // X-Shape
                        // Draw outline first if enabled
                        if (g_CrosshairOutline && outlinePen != NULL) {
                            SelectObject(hdcMem, outlinePen);
                            // Top-left to bottom-right
                            MoveToEx(hdcMem, centerX - size, centerY - size, NULL);
                            LineTo(hdcMem, centerX - gap, centerY - gap);
                            MoveToEx(hdcMem, centerX + gap, centerY + gap, NULL);
                            LineTo(hdcMem, centerX + size, centerY + size);
                            
                            // Top-right to bottom-left
                            MoveToEx(hdcMem, centerX + size, centerY - size, NULL);
                            LineTo(hdcMem, centerX + gap, centerY - gap);
                            MoveToEx(hdcMem, centerX - gap, centerY + gap, NULL);
                            LineTo(hdcMem, centerX - size, centerY + size);
                        }
                        
                        // Draw main X-shape
                        SelectObject(hdcMem, crosshairPen);
                        // Top-left to bottom-right
                        MoveToEx(hdcMem, centerX - size, centerY - size, NULL);
                        LineTo(hdcMem, centerX - gap, centerY - gap);
                        MoveToEx(hdcMem, centerX + gap, centerY + gap, NULL);
                        LineTo(hdcMem, centerX + size, centerY + size);
                        
                        // Top-right to bottom-left
                        MoveToEx(hdcMem, centerX + size, centerY - size, NULL);
                        LineTo(hdcMem, centerX + gap, centerY - gap);
                        MoveToEx(hdcMem, centerX - gap, centerY + gap, NULL);
                        LineTo(hdcMem, centerX - size, centerY + size);
                        break;
                }
                
                // Cleanup crosshair resources
                if (outlinePen) DeleteObject(outlinePen);
                if (outlineBrush) DeleteObject(outlineBrush);
                DeleteObject(crosshairPen);
                DeleteObject(crosshairBrush);
            }

            // Setup alpha blending for the crosshair
            BLENDFUNCTION blend = { AC_SRC_OVER, 0, 255, 0 };
            AlphaBlend(hdc, 0, 0, width, height, hdcMem, 0, 0, width, height, blend);
            
            // Cleanup memory DC
            SelectObject(hdcMem, hOldBitmap);
            DeleteObject(hBitmap);
            DeleteDC(hdcMem);
            
            // Draw border AFTER everything else
            HPEN borderPen = CreatePen(PS_SOLID, g_OverlayBorderThickness, RGB(
                (int)(g_AccentColor[0] * 255),
                (int)(g_AccentColor[1] * 255),
                (int)(g_AccentColor[2] * 255)
            ));
            HPEN oldPen = (HPEN)SelectObject(hdc, borderPen);
            HBRUSH oldBrush = (HBRUSH)SelectObject(hdc, GetStockObject(NULL_BRUSH));
            
            // Draw the border rectangle
            Rectangle(hdc, rect.left, rect.top, rect.right, rect.bottom);
            
            // Cleanup border resources
            SelectObject(hdc, oldPen);
            SelectObject(hdc, oldBrush);
            DeleteObject(borderPen);
            DeleteObject(blackBrush);
            
            EndPaint(hwnd, &ps);
            return 0;
        }

        case WM_KEYDOWN:
            if (wParam == VK_ESCAPE) {
                ShowOverlay(false);
            }
            return 0;
            
        // Make the overlay completely transparent to mouse events to fix sliders
        case WM_NCHITTEST:
            return HTTRANSPARENT;
            
        // No longer handle these mouse events in the overlay window
        // case WM_LBUTTONDOWN:
        // case WM_RBUTTONDOWN:
        // case WM_MBUTTONDOWN:
    }
    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

bool InitializeOverlay(HINSTANCE hInstance)
{
    // Register the window class if it doesn't exist yet
    const wchar_t CLASS_NAME[] = L"HermesOverlay";
    
    WNDCLASS wc = {};
    wc.lpfnWndProc = OverlayWindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = CLASS_NAME;
    wc.hbrBackground = NULL; // Use NULL for completely transparent background
    wc.hCursor = LoadCursor(NULL, IDC_CROSS); // Use crosshair cursor for better visibility

    if (!GetClassInfo(hInstance, CLASS_NAME, &wc)) {
        if (!RegisterClass(&wc)) {
            return false;
        }
    }

    // Create the window with transparent and click-through attributes
    g_OverlayWindow = CreateWindowEx(
        WS_EX_TOPMOST | WS_EX_LAYERED | WS_EX_TRANSPARENT | WS_EX_TOOLWINDOW | WS_EX_NOACTIVATE,  // Added WS_EX_TOOLWINDOW to hide from taskbar
        CLASS_NAME,
        L"Hermes Overlay",
        WS_POPUP,
        0, 0, GetSystemMetrics(SM_CXSCREEN), GetSystemMetrics(SM_CYSCREEN),
        NULL,
        NULL,
        hInstance,
        NULL
    );

    if (!g_OverlayWindow) {
        return false;
    }

    // Make the window fully transparent (LWA_COLORKEY would make it semi-transparent)
    // Using just LWA_ALPHA with a high opacity for the text and border only
    COLORREF transparentColor = RGB(0, 0, 0); // Black is transparent
    SetLayeredWindowAttributes(g_OverlayWindow, transparentColor, g_OverlayOpacity, LWA_COLORKEY);
    
    // Ensure the window is fully transparent to mouse events (click-through)
    // This is necessary to fix slider dragging issues
    SetWindowLong(g_OverlayWindow, GWL_EXSTYLE, 
                 GetWindowLong(g_OverlayWindow, GWL_EXSTYLE) | WS_EX_TRANSPARENT);

    // Apply current streamproof setting
    SetWindowDisplayAffinity(g_OverlayWindow, g_Streamproof ? WDA_EXCLUDEFROMCAPTURE : WDA_NONE);
    
    return true;
}

void ShowOverlay(bool show)
{
    if (!g_OverlayWindow && show) {
        // Try to initialize if it doesn't exist yet
        if (!InitializeOverlay(GetModuleHandle(NULL))) {
            printf("Failed to initialize overlay when showing\n");
            return;
        }
    }
    
    if (g_OverlayWindow) {
        ShowWindow(g_OverlayWindow, show ? SW_SHOW : SW_HIDE);
        g_OverlayVisible = show;
        printf("Overlay %s. Window handle: %p\n", show ? "shown" : "hidden", g_OverlayWindow);
        
        // Force a repaint
        if (show) {
            InvalidateRect(g_OverlayWindow, NULL, TRUE);
        }
    } else {
        printf("Cannot show/hide overlay: window handle is NULL\n");
    }
}

void UpdateOverlaySettings()
{
    if (g_OverlayWindow) {
        // Update opacity - ensure a minimum opacity for visibility
        int overlayOpacity = g_OverlayOpacity;
        if (overlayOpacity < 200) overlayOpacity = 200; // Minimum opacity of ~78% for better visibility
        
        // Set color key transparency (black background becomes transparent)
        SetLayeredWindowAttributes(g_OverlayWindow, RGB(0, 0, 0), 255, LWA_COLORKEY);
        
        // Update streamproof setting based on current state
        SetWindowDisplayAffinity(g_OverlayWindow, g_Streamproof ? WDA_EXCLUDEFROMCAPTURE : WDA_NONE);

        // Force a complete repaint to update the overlay
        InvalidateRect(g_OverlayWindow, NULL, TRUE);
        RedrawWindow(g_OverlayWindow, NULL, NULL, RDW_INVALIDATE | RDW_UPDATENOW | RDW_FRAME | RDW_ERASE | RDW_ALLCHILDREN);
    }
}

void RenderSettingsTab()
{
    ImGui::Dummy(ImVec2(0, 3));
    
    float contentWidth = ImGui::GetContentRegionAvail().x;
    float controlWidth = 200.0f;
    
    // Apply consistent text color to all text
    ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(g_TextColor[0], g_TextColor[1], g_TextColor[2], 1.0f));
    
    // Display settings
    ImGui::TextColored(ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f), "Display Settings");
    ImGui::Dummy(ImVec2(0, 3));
    
    // Style for interactive elements
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(g_AccentColor[0]*0.3f, g_AccentColor[1]*0.3f, g_AccentColor[2]*0.3f, 0.6f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(g_AccentColor[0]*0.4f, g_AccentColor[1]*0.4f, g_AccentColor[2]*0.4f, 0.7f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(g_AccentColor[0]*0.5f, g_AccentColor[1]*0.5f, g_AccentColor[2]*0.5f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(g_TextColor[0], g_TextColor[1], g_TextColor[2], 1.0f));
    ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 0.9f));
    ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.0f));
    ImGui::PushStyleColor(ImGuiCol_PopupBg, ImVec4(g_BackgroundColor[0], g_BackgroundColor[1], g_BackgroundColor[2], 0.95f));
    ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 0.5f));
    
    // Background animation toggle
    ImGui::Text("Background Animation");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::Checkbox("##BackgroundAnimation", &g_EnableBackgroundAnimation);
    
    DrawGradientSeparator(1.0f, 1.0f);
    
    // Window settings
    ImGui::TextColored(ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f), "Window Settings");
    ImGui::Dummy(ImVec2(0, 3));
    
    // Streamproof setting
    ImGui::Text("Streamproof");
    ImGui::SameLine(contentWidth - controlWidth);
    bool oldStreamproofValue = g_Streamproof;
    if (ImGui::Checkbox("##Streamproof", &g_Streamproof)) {
        if (g_Window) {
            ApplyStreamproofSetting(g_Window, g_Streamproof);
        }
        // Also update overlay window when streamproof setting changes
        if (g_OverlayWindow) {
            SetWindowDisplayAffinity(g_OverlayWindow, g_Streamproof ? WDA_EXCLUDEFROMCAPTURE : WDA_NONE);
        }
    }
    
    if (ImGui::IsItemHovered()) {
        ImGui::SetTooltip("Makes the app invisible to screen capture software");
    }
    
    // Lock position toggle with aligned checkbox
    ImGui::Text("Lock Window Position");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::Checkbox("##LockWindowPosition", &g_LockPosition);
    
    // Toggle UI key dropdown
    ImGui::Text("Toggle UI Key");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    
    // Define toggle UI key options
    const char* toggleKeys[] = { "Insert", "Home", "Page Up", "Page Down", "Delete", "End" };
    int toggleKeyValues[] = { GLFW_KEY_INSERT, GLFW_KEY_HOME, GLFW_KEY_PAGE_UP, GLFW_KEY_PAGE_DOWN, GLFW_KEY_DELETE, GLFW_KEY_END };
    
    // Find the current toggle key in our list
    static int currentToggleKeyIndex = 0;
    for (int i = 0; i < IM_ARRAYSIZE(toggleKeyValues); i++) {
        if (toggleKeyValues[i] == g_ToggleUIKey) {
            currentToggleKeyIndex = i;
            break;
        }
    }
    
    // Create the dropdown
    if (ImGui::Combo("##ToggleUIKey", &currentToggleKeyIndex, toggleKeys, IM_ARRAYSIZE(toggleKeys))) {
        g_ToggleUIKey = toggleKeyValues[currentToggleKeyIndex];
    }
    
    DrawGradientSeparator(1.0f, 1.0f);
    
    // Appearance settings
    ImGui::TextColored(ImVec4(g_AccentColor[0], g_AccentColor[1], g_AccentColor[2], 1.00f), "Appearance Settings");
    ImGui::Dummy(ImVec2(0, 3));
    
    // Menu transparency with aligned slider
    ImGui::Text("Menu Transparency");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    ImGui::SliderInt("##MenuTransparency", &g_MenuTransparency, 0, 100, "%d%%");
    
    // Set style for color pickers
    ImGui::PushStyleVar(ImGuiStyleVar_FrameBorderSize, 0.0f);
    
    // Store previous accent color to detect changes
    float prev_accent_color[3] = { g_AccentColor[0], g_AccentColor[1], g_AccentColor[2] };
    float prev_text_color[3] = { g_TextColor[0], g_TextColor[1], g_TextColor[2] };
    
    // Accent color with aligned picker
    ImGui::Text("Accent Color");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    bool accent_changed = ImGui::ColorEdit3("##AccentColor", g_AccentColor, ImGuiColorEditFlags_DisplayRGB | ImGuiColorEditFlags_InputRGB);
    
    // Text color with aligned picker
    ImGui::Text("Text Color");
    ImGui::SameLine(contentWidth - controlWidth);
    ImGui::SetNextItemWidth(controlWidth);
    bool text_changed = ImGui::ColorEdit3("##TextColor", g_TextColor, ImGuiColorEditFlags_DisplayRGB | ImGuiColorEditFlags_InputRGB);
    
    // If colors changed, immediately reapply the theme
    if (accent_changed || text_changed) {
        ApplyCustomTheme(true, 1.0f);
        // Also update overlay to reflect accent color changes
        UpdateOverlaySettings();
    }
    
    ImGui::PopStyleVar();
    
    ImGui::PopStyleColor(8); // Pop all interactive element styles
    ImGui::PopStyleColor(1); // Pop text color
}