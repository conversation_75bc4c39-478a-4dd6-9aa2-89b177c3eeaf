Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FennecClient.App", "FennecClient.App\FennecClient.App.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FennecClient.Core", "FennecClient.Core\FennecClient.Core.csproj", "{79EE6D8B-3B9D-44DE-9E32-C4765D5B4B7A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FennecClient.UI", "FennecClient.UI\FennecClient.UI.csproj", "{4C2E8D03-3F82-454E-8D1F-163E5D6AF6C9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FennecClient.Services", "FennecClient.Services\FennecClient.Services.csproj", "{3A528104-3B9A-4723-AF8B-15E29AFA942A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FennecClient.Setup", "FennecClient.Setup\FennecClient.Setup.csproj", "{8B22F33C-0B50-4A90-AB1E-48E3C34D0FB9}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "FennecClient.Native", "FennecClient.Native\FennecClient.Native.vcxproj", "{B12702AD-ABFB-343A-A199-8E24837244A3}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.Build.0 = Release|Any CPU
		{79EE6D8B-3B9D-44DE-9E32-C4765D5B4B7A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{79EE6D8B-3B9D-44DE-9E32-C4765D5B4B7A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{79EE6D8B-3B9D-44DE-9E32-C4765D5B4B7A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{79EE6D8B-3B9D-44DE-9E32-C4765D5B4B7A}.Debug|x64.Build.0 = Debug|Any CPU
		{79EE6D8B-3B9D-44DE-9E32-C4765D5B4B7A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{79EE6D8B-3B9D-44DE-9E32-C4765D5B4B7A}.Release|Any CPU.Build.0 = Release|Any CPU
		{79EE6D8B-3B9D-44DE-9E32-C4765D5B4B7A}.Release|x64.ActiveCfg = Release|Any CPU
		{79EE6D8B-3B9D-44DE-9E32-C4765D5B4B7A}.Release|x64.Build.0 = Release|Any CPU
		{4C2E8D03-3F82-454E-8D1F-163E5D6AF6C9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4C2E8D03-3F82-454E-8D1F-163E5D6AF6C9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4C2E8D03-3F82-454E-8D1F-163E5D6AF6C9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{4C2E8D03-3F82-454E-8D1F-163E5D6AF6C9}.Debug|x64.Build.0 = Debug|Any CPU
		{4C2E8D03-3F82-454E-8D1F-163E5D6AF6C9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4C2E8D03-3F82-454E-8D1F-163E5D6AF6C9}.Release|Any CPU.Build.0 = Release|Any CPU
		{4C2E8D03-3F82-454E-8D1F-163E5D6AF6C9}.Release|x64.ActiveCfg = Release|Any CPU
		{4C2E8D03-3F82-454E-8D1F-163E5D6AF6C9}.Release|x64.Build.0 = Release|Any CPU
		{3A528104-3B9A-4723-AF8B-15E29AFA942A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3A528104-3B9A-4723-AF8B-15E29AFA942A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3A528104-3B9A-4723-AF8B-15E29AFA942A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3A528104-3B9A-4723-AF8B-15E29AFA942A}.Debug|x64.Build.0 = Debug|Any CPU
		{3A528104-3B9A-4723-AF8B-15E29AFA942A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3A528104-3B9A-4723-AF8B-15E29AFA942A}.Release|Any CPU.Build.0 = Release|Any CPU
		{3A528104-3B9A-4723-AF8B-15E29AFA942A}.Release|x64.ActiveCfg = Release|Any CPU
		{3A528104-3B9A-4723-AF8B-15E29AFA942A}.Release|x64.Build.0 = Release|Any CPU
		{8B22F33C-0B50-4A90-AB1E-48E3C34D0FB9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B22F33C-0B50-4A90-AB1E-48E3C34D0FB9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B22F33C-0B50-4A90-AB1E-48E3C34D0FB9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8B22F33C-0B50-4A90-AB1E-48E3C34D0FB9}.Debug|x64.Build.0 = Debug|Any CPU
		{8B22F33C-0B50-4A90-AB1E-48E3C34D0FB9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B22F33C-0B50-4A90-AB1E-48E3C34D0FB9}.Release|Any CPU.Build.0 = Release|Any CPU
		{8B22F33C-0B50-4A90-AB1E-48E3C34D0FB9}.Release|x64.ActiveCfg = Release|Any CPU
		{8B22F33C-0B50-4A90-AB1E-48E3C34D0FB9}.Release|x64.Build.0 = Release|Any CPU
		{B12702AD-ABFB-343A-A199-8E24837244A3}.Debug|Any CPU.ActiveCfg = Debug|x64
		{B12702AD-ABFB-343A-A199-8E24837244A3}.Debug|Any CPU.Build.0 = Debug|x64
		{B12702AD-ABFB-343A-A199-8E24837244A3}.Debug|x64.ActiveCfg = Debug|x64
		{B12702AD-ABFB-343A-A199-8E24837244A3}.Debug|x64.Build.0 = Debug|x64
		{B12702AD-ABFB-343A-A199-8E24837244A3}.Release|Any CPU.ActiveCfg = Release|x64
		{B12702AD-ABFB-343A-A199-8E24837244A3}.Release|Any CPU.Build.0 = Release|x64
		{B12702AD-ABFB-343A-A199-8E24837244A3}.Release|x64.ActiveCfg = Release|x64
		{B12702AD-ABFB-343A-A199-8E24837244A3}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789012}
	EndGlobalSection
	GlobalSection(StartupProject) = preSolution
		StartupProject = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
	EndGlobalSection
EndGlobal
 