using FennecClient.Core.Models;
using System.Text.Json;
using System;
using System.IO;

namespace FennecClient.Services
{
    public class ConfigurationService
    {
        private readonly string _configPath;
        private AppConfiguration _configuration;

        public ConfigurationService()
        {
            _configPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "FennecClient", "config.json");
            _configuration = LoadConfiguration();
        }

        public AppConfiguration GetConfiguration()
        {
            return _configuration;
        }

        public void SaveConfiguration(AppConfiguration configuration)
        {
            _configuration = configuration;
            Directory.CreateDirectory(Path.GetDirectoryName(_configPath));
            var json = JsonSerializer.Serialize(_configuration, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(_configPath, json);
        }

        private AppConfiguration LoadConfiguration()
        {
            if (File.Exists(_configPath))
            {
                try
                {
                    var json = File.ReadAllText(_configPath);
                    return JsonSerializer.Deserialize<AppConfiguration>(json) ?? new AppConfiguration();
                }
                catch
                {
                    return new AppConfiguration();
                }
            }
            return new AppConfiguration();
        }
    }
} 