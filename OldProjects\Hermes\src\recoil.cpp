#include "recoil.h"
#include <random>
#include <chrono>
#include <cmath>
#include <string>
#include <iostream>
#include <algorithm> // For std::transform
#include <set>

// For debugging
#include <Windows.h>
#include <sstream>

// External reference to allowed game executables from main.cpp
extern std::set<std::string> g_AllowedGameExecutables;

// Helper for debug messages - now using console output
void ShowDebugMessage(const std::string& message) {
    printf("[RECOIL] %s\n", message.c_str());
    // Also output to debug stream for tools like DebugView
    OutputDebugStringA(("[RECOIL] " + message + "\n").c_str());
}

// Static instance for singleton pattern
static RecoilControl* s_instance = nullptr;

// Singleton access implementation
RecoilControl& RecoilControl::getInstance()
{
    if (!s_instance) {
        s_instance = new RecoilControl();
    }
    return *s_instance;
}

// Private constructor implementation
RecoilControl::RecoilControl()
    : m_enabled(false),
    m_firing(false),
    m_toggleMode(false),
    m_activationKey(VK_LBUTTON), // Default to left mouse button
    m_targetWindow(NULL),
    m_applicationWindow(NULL),
    m_inputDevice(RECOIL_INPUT_MOUSE), // Mouse only
    m_verticalRecoil(0),
    m_horizontalRecoil(0),
    m_smoothness(0.5f),
    m_randomization(0.2f),
    m_currentStep(0),
    m_lastUpdateTime(0),
    m_fireStartTime(0),
    m_currentX(0.0f),
    m_currentY(0.0f),
    m_mouseOverAppWindow(false),
    m_lastUserMouseTime(0),
    m_recoilActive(false),
    m_ignoreNextMouseMove(false)
{
    initialize();
}

// Initialize the recoil control system
bool RecoilControl::initialize()
{
    // Reset internal state
    resetRecoilState();
    return true;
}

// Shutdown and cleanup
void RecoilControl::shutdown()
{
    // Clean up any resources
    m_enabled = false;
}

// Enable/disable recoil control
void RecoilControl::setEnabled(bool enabled)
{
    m_enabled = enabled;
    if (!enabled) {
        resetRecoilState();
    }
}

bool RecoilControl::isEnabled() const
{
    return m_enabled;
}

// Set target window handle
void RecoilControl::setTargetWindow(HWND targetWindow)
{
    m_targetWindow = targetWindow;
}

HWND RecoilControl::getTargetWindow() const
{
    return m_targetWindow;
}

bool RecoilControl::isTargetWindowActive() const
{
    return (m_targetWindow != NULL && m_targetWindow == GetForegroundWindow());
}

// Vertical recoil control (-25 to 25 instead of -50 to 50)
void RecoilControl::setVerticalRecoil(int amount)
{
    m_verticalRecoil = max(-25, min(25, amount));
}

int RecoilControl::getVerticalRecoil() const
{
    return m_verticalRecoil;
}

// Horizontal recoil control (-25 to 25 instead of -50 to 50)
void RecoilControl::setHorizontalRecoil(int amount)
{
    m_horizontalRecoil = max(-25, min(25, amount));
}

int RecoilControl::getHorizontalRecoil() const
{
    return m_horizontalRecoil;
}

// Smoothness setting (0.0 - 1.0)
void RecoilControl::setSmoothness(float smoothness)
{
    m_smoothness = max(0.0f, min(1.0f, smoothness));
}

float RecoilControl::getSmoothness() const
{
    return m_smoothness;
}

// Randomization setting (0.0 - 1.0)
void RecoilControl::setRandomization(float randomness)
{
    m_randomization = max(0.0f, min(1.0f, randomness));
}

float RecoilControl::getRandomization() const
{
    return m_randomization;
}

// Activation key configuration
void RecoilControl::setActivationKey(int key)
{
    m_activationKey = key;
}

int RecoilControl::getActivationKey() const
{
    return m_activationKey;
}

// Toggle mode (on/off vs. hold)
void RecoilControl::setToggleMode(bool toggle)
{
    m_toggleMode = toggle;
}

bool RecoilControl::isToggleMode() const
{
    return m_toggleMode;
}

// Input device selection
void RecoilControl::setInputDevice(RecoilInputDevice device)
{
    m_inputDevice = device;
    resetRecoilState(); // Reset state when changing input device
}

RecoilInputDevice RecoilControl::getInputDevice() const
{
    return m_inputDevice;
}



// Application window handle
void RecoilControl::setApplicationWindow(HWND appWindow)
{
    m_applicationWindow = appWindow;
}

// Check if app window is active
bool RecoilControl::isApplicationWindowActive() const
{
    return (m_applicationWindow != NULL && m_applicationWindow == GetForegroundWindow());
}

// Set mouse over app window flag
void RecoilControl::setMouseOverAppWindow(bool isOver)
{
    m_mouseOverAppWindow = isOver;
}

bool RecoilControl::isMouseOverAppWindow() const
{
    return m_mouseOverAppWindow;
}

// Handle mouse button events
void RecoilControl::onMouseButtonEvent(int button, bool pressed)
{
    // Only process if recoil control is enabled and we're using mouse input
    if (!m_enabled || m_inputDevice != RECOIL_INPUT_MOUSE) {
        return;
    }

    // Check if this is our activation button
    if (button == m_activationKey) {
        if (m_toggleMode) {
            // In toggle mode, only respond to press events
            if (pressed) {
                m_firing = !m_firing;
                if (m_firing) {
                    m_fireStartTime = GetTickCount();
                    m_currentStep = 0;
                    m_recoilActive = true;
                    m_lastUpdateTime = 0; // Set to 0 to force immediate first update
                    printf("[RECOIL] Toggle mode: Recoil started immediately\n");
                } else {
                    resetRecoilState();
                    printf("[RECOIL] Toggle mode: Recoil stopped\n");
                }
            }
        } else {
            // In hold mode, directly set firing state
            m_firing = pressed;

            if (m_firing) {
                m_fireStartTime = GetTickCount();
                m_currentStep = 0;
                m_recoilActive = true;
                m_lastUpdateTime = 0; // Set to 0 to force immediate first update
                printf("[RECOIL] Hold mode: Recoil started immediately\n");
            } else {
                resetRecoilState();
                printf("[RECOIL] Hold mode: Recoil stopped\n");
            }
        }
    }
}





// Main processing function
void RecoilControl::process()
{

    // Skip processing if not enabled or not firing
    if (!m_enabled || !m_firing) {
        m_recoilActive = false;
        return;
    }

    // Check if our menu is active - if so, don't apply recoil
    if (isApplicationWindowActive() || m_mouseOverAppWindow) {
        m_recoilActive = false;
        return;
    }

    unsigned long currentTime = GetTickCount();

    // Reduce mouse movement interference - only pause for 50ms instead of 100ms
    if (currentTime - m_lastUserMouseTime < 50) {
        // User is actively moving mouse, pause recoil compensation briefly
        return;
    }

    // Faster window checking for immediate response
    static HWND lastCheckedGameWindow = NULL;
    static unsigned long lastProcessCheckTime = 0;
    static bool lastInGameWindow = false;
    static bool firstRun = true;

    bool inGameWindow = false;

    // For immediate response on first run or when firing starts, check immediately
    // Otherwise, check every 100ms instead of 500ms for faster response
    HWND foregroundWindow = GetForegroundWindow();
    bool shouldCheckWindow = firstRun ||
                           (m_currentStep == 0) || // First recoil step - check immediately
                           (foregroundWindow != lastCheckedGameWindow) ||
                           (currentTime - lastProcessCheckTime > 100); // Reduced from 500ms to 100ms

    if (shouldCheckWindow) {
        firstRun = false;
        lastProcessCheckTime = currentTime;
        lastCheckedGameWindow = foregroundWindow;

        if (foregroundWindow) {
            // Check process name for supported games
            DWORD processId;
            GetWindowThreadProcessId(foregroundWindow, &processId);

            if (processId != 0) {
                HANDLE hProcess = OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION, FALSE, processId);
                if (hProcess != NULL) {
                    char processName[MAX_PATH];
                    DWORD size = sizeof(processName);
                    if (QueryFullProcessImageNameA(hProcess, 0, processName, &size)) {
                        // Extract just the executable name
                        const char* filename = strrchr(processName, '\\');
                        if (filename != NULL) {
                            filename++; // Skip the backslash
                            std::string exeName = filename;
                            std::transform(exeName.begin(), exeName.end(), exeName.begin(), ::tolower);

                            // Check against l3_.ini config first, then fallback to all supported games
                            bool isAllowedGame = false;

                            // If we have a config file with specific games, use that
                            if (!g_AllowedGameExecutables.empty()) {
                                isAllowedGame = (g_AllowedGameExecutables.find(exeName) != g_AllowedGameExecutables.end());
                            } else {
                                // Fallback: Check against all supported game executables
                                isAllowedGame = (exeName == "r5apex.exe" || exeName == "r5apex_dx12.exe" ||                    // Apex Legends
                                    exeName == "escapefromtarkov.exe" ||                                         // Escape from Tarkov
                                    exeName == "fortniteclient-win64-shipping.exe" ||                           // Fortnite
                                    exeName == "fortniteclient-win64-shipping_eac_eos.exe" ||                   // Fortnite EAC
                                    exeName == "modernwarfare.exe" ||                                            // Modern Warfare 2019
                                    exeName == "tslgame.exe" ||                                                  // PUBG
                                    exeName == "rainbowsix.exe" || exeName == "rainbowsix_be.exe" ||            // Rainbow Six Siege
                                    exeName == "rainbowsix_vulkan.exe" ||                                       // Rainbow Six Siege Vulkan
                                    exeName == "rustclient.exe" ||                                              // Rust
                                    exeName == "cod.exe" ||                                                     // Black Ops 6
                                    exeName == "cod22-cod.exe" ||                                               // Modern Warfare II
                                    exeName == "cod23-cod.exe" ||                                               // Modern Warfare III
                                    exeName == "cs2.exe" ||                                                     // Counter-Strike 2
                                    exeName == "deltaforceclient-win64-shipping.exe");                        // Delta Force
                            }

                            if (isAllowedGame) {
                                inGameWindow = true;

                                // Set this as our target window if not already set
                                if (m_targetWindow != foregroundWindow) {
                                    m_targetWindow = foregroundWindow;
                                    printf("[RECOIL] Supported game detected: %s\n", exeName.c_str());
                                }
                            }
                        }
                    }
                    CloseHandle(hProcess);
                }
            }
        }
        
        lastInGameWindow = inGameWindow;
    }
    else {
        // Use cached result to avoid expensive checks every frame
        inGameWindow = lastInGameWindow;
    }

    // If not in a supported game, don't apply recoil
    if (!inGameWindow) {
        return;
    }

    // For immediate response: Skip timing check on first recoil step
    unsigned long timeSinceLastUpdate = currentTime - m_lastUpdateTime;
    if (m_currentStep > 0 && timeSinceLastUpdate < 3) { // Reduced from 5ms to 3ms for even faster response
        return;
    }

    // Mark recoil as active
    m_recoilActive = true;

    // Calculate next recoil values
    float recoilX = 0.0f;
    float recoilY = 0.0f;
    calculateNextRecoilValues(recoilX, recoilY);

    // Apply the recoil compensation
    applyRecoilCompensation(recoilX, recoilY);

    // Update state
    m_lastUpdateTime = currentTime;
    m_currentStep++;

    // Debug output for first few steps and then every 2 seconds
    static unsigned long lastDebugOutput = 0;
    if (m_currentStep <= 5 || (currentTime - lastDebugOutput > 2000)) {
        printf("[RECOIL] Active - Step: %d, X: %.2f, Y: %.2f, TimeSinceStart: %dms\n",
               m_currentStep, recoilX, recoilY, currentTime - m_fireStartTime);
        lastDebugOutput = currentTime;
    }
}

// Calculate the next recoil compensation values
void RecoilControl::calculateNextRecoilValues(float& x, float& y)
{
    // Base recoil values, scaled to reasonable pixel ranges
    float baseVertical = m_verticalRecoil * 0.08f;  // Reduced from 0.25f to 0.08f for more subtle movement
    float baseHorizontal = m_horizontalRecoil * 0.04f; // Increased from 0.005f to 0.04f for noticeable movement

    // Pattern progression - can be customized for different recoil patterns
    // For now, using a simple time-based increase
    float progressionFactor = min(1.0f, m_currentStep / 30.0f);
    baseVertical *= (1.0f + progressionFactor * 0.3f); // Reduced from 0.7f to 0.3f for slower buildup
    baseHorizontal *= (1.0f + progressionFactor * 0.2f); // Add progression to horizontal recoil too

    // Add randomization
    static std::mt19937 rng(static_cast<unsigned int>(std::chrono::system_clock::now().time_since_epoch().count()));
    std::uniform_real_distribution<float> dist(-1.0f, 1.0f);

    float randomX = dist(rng) * m_randomization * abs(baseHorizontal) * 0.3f; // Reduced from 0.5f
    float randomY = dist(rng) * m_randomization * abs(baseVertical) * 0.2f; // Reduced from 0.3f

    // Calculate final recoil with smoothing
    // Use exponential smoothing formula: newValue = alpha * targetValue + (1 - alpha) * oldValue
    float smoothingFactor = 1.0f - m_smoothness * 0.95f; // Increased smoothing effect
    x = smoothingFactor * (baseHorizontal + randomX) + (1.0f - smoothingFactor) * m_currentX;
    y = smoothingFactor * (baseVertical + randomY) + (1.0f - smoothingFactor) * m_currentY;

    // Ensure y has a minimum value to guarantee movement
    // Use smaller values for minimum movement
    if (m_verticalRecoil > 0 && y < 0.2f) y = 0.2f; // Reduced from 1.0f to 0.2f
    if (m_verticalRecoil < 0 && y > -0.2f) y = -0.2f; // Reduced from -1.0f to -0.2f

    // Add similar minimum guarantees for horizontal movement
    if (m_horizontalRecoil > 0 && x < 0.2f) x = 0.2f;
    if (m_horizontalRecoil < 0 && x > -0.2f) x = -0.2f;

    // Save current values for next smoothing calculation
    m_currentX = x;
    m_currentY = y;
}

// Apply the calculated recoil compensation by moving the mouse
void RecoilControl::applyRecoilCompensation(float x, float y)
{
    // Get current cursor position
    POINT currentPos;
    if (!GetCursorPos(&currentPos)) {
        return;
    }

    // Calculate new position
    int deltaX = static_cast<int>(round(x));
    int deltaY = static_cast<int>(round(y));

    // Ensure minimum movement if vertical recoil is enabled
    if (m_verticalRecoil != 0 && abs(deltaY) < 1) {
        deltaY = (m_verticalRecoil > 0) ? 1 : -1;
    }

    // Ensure minimum movement if horizontal recoil is enabled
    if (m_horizontalRecoil != 0 && abs(deltaX) < 1) {
        deltaX = (m_horizontalRecoil > 0) ? 1 : -1;
    }

    // Skip if no movement
    if (deltaX == 0 && deltaY == 0) {
        return;
    }

    // Set flag to ignore the next mouse move event (since it's our recoil compensation)
    m_ignoreNextMouseMove = true;

    // Use SendInput to simulate mouse movement (works better with games)
    INPUT input = {0};
    input.type = INPUT_MOUSE;
    input.mi.dwFlags = MOUSEEVENTF_MOVE;
    input.mi.dx = deltaX;
    input.mi.dy = deltaY;

    // Send the input
    UINT result = SendInput(1, &input, sizeof(INPUT));
    if (result != 1) {
        // Only log errors occasionally to reduce performance impact
        static unsigned long lastErrorLogTime = 0;
        unsigned long currentTime = GetTickCount();
        if (currentTime - lastErrorLogTime > 5000) { // Only log once every 5 seconds
            DWORD error = GetLastError();
            printf("[RECOIL] Failed to send input. Error: %d\n", error);
            lastErrorLogTime = currentTime;
        }
        // Reset flag if sending failed
        m_ignoreNextMouseMove = false;
    }
}

// Reset internal state
void RecoilControl::resetRecoilState()
{
    m_currentStep = 0;
    m_lastUpdateTime = GetTickCount();
    m_fireStartTime = 0;
    m_currentX = 0.0f;
    m_currentY = 0.0f;
    m_recoilActive = false;
    m_ignoreNextMouseMove = false;
}

// Handle mouse movement events to detect user input
void RecoilControl::onMouseMoveEvent()
{
    // If we're ignoring the next mouse move (because it's our recoil compensation), skip it
    if (m_ignoreNextMouseMove) {
        m_ignoreNextMouseMove = false;
        return;
    }

    // Only record user mouse movement if recoil is not currently active
    // This prevents normal recoil compensation from being interpreted as user input
    if (!m_recoilActive) {
        m_lastUserMouseTime = GetTickCount();
    }
}

