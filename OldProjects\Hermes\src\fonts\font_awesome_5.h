#pragma once

// FontAwesome 5 Icon Definitions
namespace FontAwesome {
    // Icon Unicode points for sidebar tabs
    static const char* ICON_FA_GAMEPAD = "\xEF\x84\x9B";        // f11b - Gamepad/Status icon
    static const char* ICON_FA_SYNC = "\xEF\x80\xA1";           // f021 - Sync/Update icon (correct UTF-8 encoding)
    static const char* ICON_FA_COG = "\xEF\x80\x93";            // f013 - Cog/Settings icon
    static const char* ICON_FA_BOOK = "\xEF\x80\xAD";           // f02d - Book icon for Guide
    static const char* ICON_FA_TIMES = "\xEF\x80\x8D";          // f00d - Close/X icon

    // Additional useful icons
    static const char* ICON_FA_HOME = "\xEF\x80\x95";           // f015 - Home icon
    static const char* ICON_FA_INFO_CIRCLE = "\xEF\x81\x9A";    // f05a - Info circle icon
    static const char* ICON_FA_CHECK = "\xEF\x80\x8C";          // f00c - Check icon
    static const char* ICON_FA_EXCLAMATION_TRIANGLE = "\xEF\x81\xB1"; // f071 - Warning icon
    static const char* ICON_FA_QUESTION_CIRCLE = "\xEF\x81\x99"; // f059 - Question circle icon

    static const char* ICON_FA_USER = "\xEF\x80\x87";           // f007 - User icon
    static const char* ICON_FA_PLAY = "\xEF\x81\x8B";           // f04b - Play icon
    static const char* ICON_FA_BELL = "\xEF\x83\xB3";           // f0f3 - Bell icon (notifications/news)

    // Additional icons for sidebar tabs
    static const char* ICON_FA_CROSSHAIRS = "\xEF\x81\x9B";     // f05b - Crosshairs icon
    static const char* ICON_FA_BULLSEYE = "\xEF\x85\x80";       // f140 - Bullseye icon
    static const char* ICON_FA_FOLDER = "\xEF\x81\xBB";         // f07b - Folder icon
    static const char* ICON_FA_TARGET = "\xEF\x8E\x80";         // f3c0 - Target icon
}
