using FennecClient.Core.Interfaces;
using System.Collections.Generic;
using System;
using System.Linq;

namespace FennecClient.Services
{
    public class ModuleManager
    {
        private readonly List<IModule> _modules;

        public ModuleManager()
        {
            _modules = new List<IModule>();
        }

        public void RegisterModule(IModule module)
        {
            if (!_modules.Contains(module))
            {
                _modules.Add(module);
            }
        }

        public void InitializeModules()
        {
            foreach (var module in _modules.Where(m => m.IsEnabled))
            {
                try
                {
                    module.Initialize();
                }
                catch (Exception ex)
                {
                    // Log error - module failed to initialize
                    Console.WriteLine($"Failed to initialize module {module.Name}: {ex.Message}");
                }
            }
        }

        public void ShutdownModules()
        {
            foreach (var module in _modules.Where(m => m.IsEnabled))
            {
                try
                {
                    module.Shutdown();
                }
                catch (Exception ex)
                {
                    // Log error - module failed to shutdown
                    Console.WriteLine($"Failed to shutdown module {module.Name}: {ex.Message}");
                }
            }
        }

        public IEnumerable<IModule> GetModules()
        {
            return _modules.AsReadOnly();
        }
    }
} 