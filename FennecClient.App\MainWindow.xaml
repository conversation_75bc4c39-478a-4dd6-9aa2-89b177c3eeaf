<Window x:Class="FennecClient.App.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:FennecClient.UI.Controls;assembly=FennecClient.UI"
        Title="Fennec Client" Height="700" Width="1100" 
        Background="#151519" WindowStartupLocation="CenterScreen"
        WindowStyle="None" ResizeMode="NoResize"
        AllowsTransparency="False" BorderThickness="0"
        Topmost="True"
        ShowInTaskbar="False"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType"
        RenderOptions.BitmapScalingMode="HighQuality"
        UseLayoutRounding="True"
        SnapsToDevicePixels="True">
    
    <WindowChrome.WindowChrome>
        <WindowChrome CaptionHeight="0" GlassFrameThickness="0" NonClientFrameEdges="None"/>
    </WindowChrome.WindowChrome>
    
    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#e4e5e6"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="4,2,4,2"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Width" Value="Auto"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <Border x:Name="border" Background="{TemplateBinding Background}" 
                                    CornerRadius="5" BorderThickness="0">
                                <Border x:Name="highlight" Background="#fa0000" Opacity="0" 
                                        CornerRadius="5"/>
                            </Border>
                            <Rectangle x:Name="activeIndicator" Width="3" Height="0" 
                                       Fill="#fa0000" HorizontalAlignment="Left" 
                                       VerticalAlignment="Center" Margin="0,0,0,0" 
                                       RadiusX="2" RadiusY="2" Opacity="0"/>
                            <Border CornerRadius="5">
                                <ContentPresenter HorizontalAlignment="Left" VerticalAlignment="Center"
                                                  Margin="{TemplateBinding Padding}"/>
                            </Border>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="highlight" Property="Opacity" Value="0.1"/>
                                <Setter TargetName="border" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Black" Direction="270" 
                                                          ShadowDepth="5" BlurRadius="15" Opacity="0.3"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="highlight" Property="Opacity" Value="0.2"/>
                            </Trigger>
                            <Trigger Property="Tag" Value="Active">
                                <Setter TargetName="border" Property="Background" Value="#333338"/>
                                <Setter TargetName="activeIndicator" Property="Height" Value="40"/>
                                <Setter TargetName="activeIndicator" Property="Opacity" Value="1"/>
                                <Setter TargetName="border" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Black" Direction="270" 
                                                          ShadowDepth="8" BlurRadius="20" Opacity="0.4"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="Border">
            <Setter Property="Background" Value="#222328"/>
            <Setter Property="CornerRadius" Value="16"/>
            <Setter Property="Padding" Value="24"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="8" 
                                      BlurRadius="25" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Minimize Button Style -->
        <Style x:Key="MinimizeButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#e4e5e6"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="44"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" Background="{TemplateBinding Background}" 
                                CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#4CAF50"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Maximize Button Style -->
        <Style x:Key="MaximizeButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#e4e5e6"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="44"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" Background="{TemplateBinding Background}" 
                                CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FF9800"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Close Button Style -->
        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#e4e5e6"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="44"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" Background="{TemplateBinding Background}" 
                                CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#fa0000"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="220"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        
        <!-- Modern Sidebar -->
        <Border Grid.Column="0" Background="#222328" 
                MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="0" ShadowDepth="15" 
                                  BlurRadius="30" Opacity="0.4"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- Logo Section -->
                <Border Grid.Row="0" Margin="24,32,24,24">
                    <StackPanel>
                        <Border Background="Transparent" Height="80" Width="80" 
                                CornerRadius="40" HorizontalAlignment="Center">
                            <Grid>
                                <Image x:Name="LogoImage" Source="resources/Logo.png" 
                                       Height="100" Width="Auto" 
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                <TextBlock Text="Fennec" FontSize="32" FontWeight="Bold" 
                                           Foreground="#fa0000" HorizontalAlignment="Center" 
                                           VerticalAlignment="Center" x:Name="LogoFallback" 
                                           Visibility="Collapsed"/>
                            </Grid>
                        </Border>
                    </StackPanel>
                </Border>
                
                <!-- Navigation -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Hidden" 
                              HorizontalScrollBarVisibility="Disabled" Margin="5,0">
                    <StackPanel Margin="12,24,12,12">
                        
                        <!-- MNK Tab -->
                        <Button x:Name="MNKButton" Style="{StaticResource ModernButtonStyle}" 
                                Click="NavigateToTab" Margin="0,0,0,6">
                            <Grid Height="30">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="48"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <Border Grid.Column="0" Background="#fa0000" Width="32" Height="32" 
                                        CornerRadius="8" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <TextBlock Text="&#xE765;" FontFamily="Segoe MDL2 Assets" FontSize="16"
                                               Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                
                                <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="8,0,0,0">
                                    <TextBlock Text="MNK" FontSize="14" FontWeight="SemiBold" 
                                               Foreground="#e4e5e6" Margin="0,0,0,2"/>
                                    <TextBlock Text="Mouse &amp; Keyboard" FontSize="10" 
                                               Foreground="#888888"/>
                                </StackPanel>
                            </Grid>
                        </Button>
                        
                        <!-- Controller Tab -->
                        <Button x:Name="ControllerButton" Style="{StaticResource ModernButtonStyle}" 
                                Click="NavigateToTab" Margin="0,0,0,6">
                            <Grid Height="30">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="48"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <Border Grid.Column="0" Background="#fa0000" Width="32" Height="32" 
                                        CornerRadius="8" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <TextBlock Text="&#xE7FC;" FontFamily="Segoe MDL2 Assets" FontSize="16"
                                               Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                
                                <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="8,0,0,0">
                                    <TextBlock Text="Controller" FontSize="14" FontWeight="SemiBold" 
                                               Foreground="#e4e5e6" Margin="0,0,0,2"/>
                                    <TextBlock Text="Gamepad Settings" FontSize="10" 
                                               Foreground="#888888"/>
                                </StackPanel>
                            </Grid>
                        </Button>
                        
                        <!-- Crosshair Tab -->
                        <Button x:Name="CrosshairButton" Style="{StaticResource ModernButtonStyle}" 
                                Click="NavigateToTab" Margin="0,0,0,6">
                            <Grid Height="30">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="48"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <Border Grid.Column="0" Background="#fa0000" Width="32" Height="32" 
                                        CornerRadius="8" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <TextBlock Text="&#xE890;" FontFamily="Segoe MDL2 Assets" FontSize="16"
                                               Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                
                                <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="8,0,0,0">
                                    <TextBlock Text="Visuals" FontSize="14" FontWeight="SemiBold" 
                                               Foreground="#e4e5e6" Margin="0,0,0,2"/>
                                    <TextBlock Text="Visual Settings" FontSize="10" 
                                               Foreground="#888888"/>
                                </StackPanel>
                            </Grid>
                        </Button>
                        
                        
                        <!-- Configs Tab -->
                        <Button x:Name="ConfigsButton" Style="{StaticResource ModernButtonStyle}" 
                                Click="NavigateToTab" Margin="0,0,0,6">
                            <Grid Height="30">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="48"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <Border Grid.Column="0" Background="#fa0000" Width="32" Height="32" 
                                        CornerRadius="8" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <TextBlock Text="&#xE8B7;" FontFamily="Segoe MDL2 Assets" FontSize="16"
                                               Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                
                                <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="8,0,0,0">
                                    <TextBlock Text="Configs" FontSize="14" FontWeight="SemiBold" 
                                               Foreground="#e4e5e6" Margin="0,0,0,2"/>
                                    <TextBlock Text="Save &amp; Load" FontSize="10" 
                                               Foreground="#888888"/>
                                </StackPanel>
                            </Grid>
                        </Button>
                        
                        <!-- Overview Tab -->
                        <Button x:Name="OverviewButton" Style="{StaticResource ModernButtonStyle}" 
                                Click="NavigateToTab" Tag="Active" Margin="0,0,0,6">
                            <Grid Height="30">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="48"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <Border Grid.Column="0" Background="#fa0000" Width="32" Height="32" 
                                        CornerRadius="8" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <TextBlock Text="&#xE713;" FontFamily="Segoe MDL2 Assets" FontSize="16"
                                               Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                
                                <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="8,0,0,0">
                                    <TextBlock Text="Overview" FontSize="14" FontWeight="SemiBold" 
                                               Foreground="#e4e5e6" Margin="0,0,0,2"/>
                                    <TextBlock Text="System Info" FontSize="10" 
                                               Foreground="#888888"/>
                                </StackPanel>
                            </Grid>
                        </Button>
                    </StackPanel>
                </ScrollViewer>
                
                <!-- Streamproof Section -->
                <Border Grid.Row="2" Margin="12,0,12,16" 
                        Background="#333338" CornerRadius="12" Padding="16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Icon -->
                        <Border Grid.Column="0" Background="#fa0000" Width="28" Height="28" 
                                CornerRadius="6" HorizontalAlignment="Center" VerticalAlignment="Center">
                            <TextBlock Text="&#xE72E;" FontFamily="Segoe MDL2 Assets" FontSize="14"
                                       Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        
                        <!-- Text -->
                        <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="12,0,0,0">
                            <TextBlock Text="Streamproof" FontSize="13" FontWeight="SemiBold" 
                                       Foreground="#e4e5e6" Margin="0,0,0,1"/>
                            <TextBlock Text="OBS/Discord geschützt" FontSize="9" 
                                       Foreground="#888888"/>
                        </StackPanel>
                        
                        <!-- Toggle -->
                        <ToggleButton x:Name="StreamproofToggle" Grid.Column="2" 
                                      IsChecked="True" Click="StreamproofToggle_Click"
                                      Width="40" Height="20" BorderThickness="0" 
                                      Background="Transparent" Cursor="Hand">
                            <ToggleButton.Template>
                                <ControlTemplate TargetType="ToggleButton">
                                    <Border x:Name="border" Background="#666666" CornerRadius="10" 
                                            Width="40" Height="20" BorderThickness="0">
                                        <Ellipse x:Name="thumb" Fill="White" Width="16" Height="16" 
                                                 HorizontalAlignment="Left" Margin="2,0,0,0">
                                            <Ellipse.RenderTransform>
                                                <TranslateTransform x:Name="thumbTransform" X="0"/>
                                            </Ellipse.RenderTransform>
                                        </Ellipse>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsChecked" Value="True">
                                            <Setter TargetName="border" Property="Background" Value="#fa0000"/>
                                            <Trigger.EnterActions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation Storyboard.TargetName="thumbTransform" 
                                                                       Storyboard.TargetProperty="X" 
                                                                       To="20" Duration="0:0:0.2"/>
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </Trigger.EnterActions>
                                            <Trigger.ExitActions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <DoubleAnimation Storyboard.TargetName="thumbTransform" 
                                                                       Storyboard.TargetProperty="X" 
                                                                       To="0" Duration="0:0:0.2"/>
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </Trigger.ExitActions>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </ToggleButton.Template>
                        </ToggleButton>
                    </Grid>
                </Border>
            </Grid>
        </Border>
        
        <!-- Main Content Area -->
        <Grid Grid.Column="1" Background="#151519">
            <!-- Content Area -->
            <Grid x:Name="ContentArea" Margin="5,5,5,5">
                
                <!-- MNK Content -->
                <Grid x:Name="MNKContent" Visibility="Collapsed">
                    <ScrollViewer VerticalScrollBarVisibility="Hidden" HorizontalScrollBarVisibility="Disabled">
                        <StackPanel>
                            <!-- Mouse Recoil Widget -->
                            <local:AntiRecoilWidget VerticalAlignment="Top"/>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
                
                <!-- Controller Content -->
                <Grid x:Name="ControllerContent" Visibility="Collapsed">
                    <ScrollViewer VerticalScrollBarVisibility="Hidden" HorizontalScrollBarVisibility="Disabled">
                        <StackPanel>
                            <!-- Xbox Recoil Widget -->
                            <local:XboxRecoilWidget VerticalAlignment="Top"/>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
                
                <!-- Crosshair Content -->
                <Grid x:Name="CrosshairContent" Visibility="Collapsed">
                    <ScrollViewer VerticalScrollBarVisibility="Hidden" HorizontalScrollBarVisibility="Disabled">
                        <StackPanel>
                            <!-- Crosshair Widget -->
                            <local:CrosshairWidget VerticalAlignment="Top"/>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
                
                
                <!-- Configs Content -->
                <Grid x:Name="ConfigsContent" Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Text="Configs" FontSize="32" FontWeight="Light" 
                                   Foreground="#e4e5e6" Margin="0,0,0,16"/>
                        <TextBlock Text="Load, save and manage different configuration profiles." 
                                   Foreground="#888888" FontSize="16"/>
                    </StackPanel>
                </Grid>
                
                <!-- Overview Content -->
                <Grid x:Name="OverviewContent" Visibility="Visible">
                    <StackPanel>
                        <TextBlock Text="System Overview" FontSize="32" FontWeight="Light" 
                                   Foreground="#e4e5e6" Margin="0,0,0,16"/>
                        <TextBlock Text="System overview and application information." 
                                   Foreground="#888888" FontSize="16"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Grid>
    </Grid>
</Window> 