using System;
using System.Windows;

namespace FennecClient.App
{
    public class Program
    {
        [STAThread]
        public static void Main()
        {
            // Allocate console for debug output
            AllocConsole();
            
            Console.WriteLine("=== Fennec Client Debug Console ===");
            Console.WriteLine("App starting...");
            Console.WriteLine("");
            
            var app = new Application();
            app.ShutdownMode = ShutdownMode.OnMainWindowClose;
            
            app.Exit += (s, e) =>
            {
                Console.WriteLine("");
                Console.WriteLine("=== Application Exited ===");
                Console.WriteLine("Press any key to close console...");
                Console.ReadKey();
                FreeConsole();
            };
            
            var mainWindow = new MainWindow();
            app.Run(mainWindow);
        }
        
        [System.Runtime.InteropServices.DllImport("kernel32.dll")]
        static extern bool AllocConsole();
        
        [System.Runtime.InteropServices.DllImport("kernel32.dll")]
        static extern bool FreeConsole();
    }
} 