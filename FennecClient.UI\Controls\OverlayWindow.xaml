<Window x:Class="FennecClient.UI.Controls.OverlayWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Fennec Game Overlay" 
        WindowStyle="None" 
        AllowsTransparency="True" 
        Background="Transparent"
        Topmost="True"
        ShowInTaskbar="False"
        ResizeMode="NoResize"
        WindowState="Maximized">
    

        
    <Grid>
        <!-- Crosshair Center -->
        <Canvas x:Name="CrosshairCanvas" HorizontalAlignment="Center" VerticalAlignment="Center">
            <!-- Horizontal Line -->
            <Rectangle x:Name="CrosshairHorizontal" Width="20" Height="2" Fill="#fa0000" 
                        Canvas.Left="-10" Canvas.Top="-1" Visibility="Collapsed">
                <Rectangle.Effect>
                    <DropShadowEffect Color="#fa0000" Direction="0" ShadowDepth="0" BlurRadius="6" Opacity="1"/>
                </Rectangle.Effect>
            </Rectangle>
            <!-- Vertical Line -->
            <Rectangle x:Name="CrosshairVertical" Width="2" Height="20" Fill="#fa0000" 
                        Canvas.Left="-1" Canvas.Top="-10" Visibility="Collapsed">
                <Rectangle.Effect>
                    <DropShadowEffect Color="#fa0000" Direction="0" ShadowDepth="0" BlurRadius="6" Opacity="1"/>
                </Rectangle.Effect>
            </Rectangle>
        </Canvas>
    </Grid>
</Window> 