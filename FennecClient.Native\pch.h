#pragma once

#include <Windows.h>
#include <memory>
#include <vector>
#include <mutex>
#include <thread>
#include <atomic>
#include <chrono>
#include <set>
#include <string>
#include <algorithm>
#include <random>
#include <cmath>
#include <iostream>
#include <sstream>
#include <TlHelp32.h>
#include <Psapi.h>

// Define FENNECCLIENTNATIVE_EXPORTS when building the DLL
#ifndef FENNECCLIENTNATIVE_EXPORTS
#define FENNECCLIENTNATIVE_EXPORTS
#endif
