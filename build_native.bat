@echo off
echo Building FennecClient.Native...

REM Check if Visual Studio is available
where msbuild >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo MSBuild not found. Please run this from a Visual Studio Developer Command Prompt.
    pause
    exit /b 1
)

REM Build the native C++ project
echo Building Debug configuration...
msbuild FennecClient.Native\FennecClient.Native.vcxproj /p:Configuration=Debug /p:Platform=x64

if %ERRORLEVEL% NEQ 0 (
    echo Debug build failed!
    pause
    exit /b 1
)

echo Building Release configuration...
msbuild FennecClient.Native\FennecClient.Native.vcxproj /p:Configuration=Release /p:Platform=x64

if %ERRORLEVEL% NEQ 0 (
    echo Release build failed!
    pause
    exit /b 1
)

echo Native build completed successfully!
echo.
echo DLL files should be in:
echo - FennecClient.App\bin\Debug\net8.0-windows\FennecClient.Native.dll
echo - FennecClient.App\bin\Release\net8.0-windows\FennecClient.Native.dll
echo.
pause
