using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows.Threading;

namespace FennecClient.UI.Controls
{
    public partial class AntiRecoilWidget : UserControl
    {
        // Enhanced input APIs for better game compatibility
        [DllImport("user32.dll")]
        private static extern short GetAsyncKeyState(int vKey);

        [DllImport("user32.dll", SetLastError = true)]
        private static extern uint SendInput(uint nInputs, INPUT[] pInputs, int cbSize);

        [DllImport("user32.dll")]
        private static extern IntPtr GetMessageExtraInfo();

        // Hardware-level mouse control
        [DllImport("user32.dll")]
        private static extern bool SetCursorPos(int x, int y);

        [DllImport("user32.dll")]
        private static extern bool GetCursorPos(out POINT lpPoint);

        // Low-level mouse hook for hardware simulation
        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr SetWindowsHookEx(int idHook, LowLevelMouseProc lpfn, IntPtr hMod, uint dwThreadId);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool UnhookWindowsHookEx(IntPtr hhk);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr CallNextHookEx(IntPtr hhk, int nCode, IntPtr wParam, IntPtr lParam);

        [DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr GetModuleHandle(string lpModuleName);

        // Mouse hook delegate
        private delegate IntPtr LowLevelMouseProc(int nCode, IntPtr wParam, IntPtr lParam);

        // Hook constants
        private const int WH_MOUSE_LL = 14;
        private const int WM_MOUSEMOVE = 0x0200;

        // Kernel-level APIs for direct device access
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr CreateFile(string lpFileName, uint dwDesiredAccess, uint dwShareMode,
            IntPtr lpSecurityAttributes, uint dwCreationDisposition, uint dwFlagsAndAttributes, IntPtr hTemplateFile);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool DeviceIoControl(IntPtr hDevice, uint dwIoControlCode, IntPtr lpInBuffer,
            uint nInBufferSize, IntPtr lpOutBuffer, uint nOutBufferSize, out uint lpBytesReturned, IntPtr lpOverlapped);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool CloseHandle(IntPtr hObject);

        // Device access constants
        private const uint GENERIC_WRITE = 0x40000000;
        private const uint FILE_SHARE_READ = 0x00000001;
        private const uint FILE_SHARE_WRITE = 0x00000002;
        private const uint OPEN_EXISTING = 3;
        private const uint FILE_ATTRIBUTE_NORMAL = 0x80;
        private const uint IOCTL_HID_SET_FEATURE = 0xB0191;

        // Memory patching APIs
        [DllImport("kernel32.dll")]
        private static extern IntPtr OpenProcess(uint dwDesiredAccess, bool bInheritHandle, uint dwProcessId);

        [DllImport("kernel32.dll")]
        private static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer,
            uint dwSize, out uint lpNumberOfBytesRead);

        [DllImport("kernel32.dll")]
        private static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer,
            uint dwSize, out uint lpNumberOfBytesWritten);

        [DllImport("user32.dll")]
        private static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);

        [DllImport("user32.dll")]
        private static extern IntPtr GetForegroundWindow();

        // Process access constants
        private const uint PROCESS_ALL_ACCESS = 0x1F0FFF;

        // Input structures for SendInput
        [StructLayout(LayoutKind.Sequential)]
        public struct INPUT
        {
            public uint type;
            public InputUnion u;
        }

        [StructLayout(LayoutKind.Explicit)]
        public struct InputUnion
        {
            [FieldOffset(0)]
            public MOUSEINPUT mi;
            [FieldOffset(0)]
            public KEYBDINPUT ki;
            [FieldOffset(0)]
            public HARDWAREINPUT hi;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct MOUSEINPUT
        {
            public int dx;
            public int dy;
            public uint mouseData;
            public uint dwFlags;
            public uint time;
            public IntPtr dwExtraInfo;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct KEYBDINPUT
        {
            public ushort wVk;
            public ushort wScan;
            public uint dwFlags;
            public uint time;
            public IntPtr dwExtraInfo;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct HARDWAREINPUT
        {
            public uint uMsg;
            public ushort wParamL;
            public ushort wParamH;
        }

        // Constants
        private const uint INPUT_MOUSE = 0;
        private const uint MOUSEEVENTF_MOVE = 0x0001;
        private const uint MOUSEEVENTF_ABSOLUTE = 0x8000;
        private const int VK_LBUTTON = 0x01;
        private const int VK_RBUTTON = 0x02;
        
        private DispatcherTimer recoilTimer;
        private bool isRecoilActive = false;
        private DateTime recoilStartTime;
        private Random random = new Random(); // For anti-detection

        // Hardware-level mouse control
        private IntPtr mouseHookID = IntPtr.Zero;
        private LowLevelMouseProc mouseProc;
        private bool useHardwareLevel = true;
        private Queue<POINT> pendingMovements = new Queue<POINT>();

        // Kernel-level input injection
        private bool useKernelLevel = true;
        private IntPtr deviceHandle = IntPtr.Zero;

        // Memory patching for direct game manipulation
        private bool useMemoryPatching = true;
        private IntPtr gameProcessHandle = IntPtr.Zero;
        
        public AntiRecoilWidget()
        {
            InitializeComponent();
            InitializeRecoilSystem();
            InitializeHardwareLevel();
        }
        
        private void InitializeRecoilSystem()
        {
            recoilTimer = new DispatcherTimer();
            recoilTimer.Interval = TimeSpan.FromMilliseconds(2); // Ultra-fast response for competitive gaming
            recoilTimer.Tick += RecoilTimer_Tick;

            MouseRecoilToggle.Checked += (s, e) => StartRecoilMonitoring();
            MouseRecoilToggle.Unchecked += (s, e) => StopRecoilMonitoring();
        }

        private void InitializeHardwareLevel()
        {
            try
            {
                mouseProc = MouseHookCallback;
                mouseHookID = SetMouseHook(mouseProc);
                Console.WriteLine("Hardware-level mouse hook initialized successfully");

                // Try to initialize kernel-level access
                InitializeKernelLevel();

                // Try to initialize memory patching
                InitializeMemoryPatching();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize hardware-level hook: {ex.Message}");
                useHardwareLevel = false;
            }
        }

        private void InitializeKernelLevel()
        {
            try
            {
                // Try to open direct device access to HID mouse
                deviceHandle = CreateFile("\\\\.\\HID#VID_046D&PID_C52B#", // Generic HID mouse path
                    GENERIC_WRITE, FILE_SHARE_READ | FILE_SHARE_WRITE, IntPtr.Zero,
                    OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, IntPtr.Zero);

                if (deviceHandle != IntPtr.Zero && deviceHandle.ToInt32() != -1)
                {
                    Console.WriteLine("Kernel-level device access initialized successfully");
                    useKernelLevel = true;
                }
                else
                {
                    Console.WriteLine("Kernel-level device access failed, falling back to user-mode");
                    useKernelLevel = false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize kernel-level access: {ex.Message}");
                useKernelLevel = false;
            }
        }

        private void InitializeMemoryPatching()
        {
            try
            {
                // Get the foreground window (game window)
                IntPtr foregroundWindow = GetForegroundWindow();
                if (foregroundWindow != IntPtr.Zero)
                {
                    uint processId;
                    GetWindowThreadProcessId(foregroundWindow, out processId);

                    if (processId != 0)
                    {
                        gameProcessHandle = OpenProcess(PROCESS_ALL_ACCESS, false, processId);
                        if (gameProcessHandle != IntPtr.Zero)
                        {
                            Console.WriteLine($"Memory patching initialized for process ID: {processId}");
                            useMemoryPatching = true;
                        }
                        else
                        {
                            Console.WriteLine("Failed to open game process for memory patching");
                            useMemoryPatching = false;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize memory patching: {ex.Message}");
                useMemoryPatching = false;
            }
        }

        private IntPtr SetMouseHook(LowLevelMouseProc proc)
        {
            using (var curProcess = System.Diagnostics.Process.GetCurrentProcess())
            using (var curModule = curProcess.MainModule)
            {
                return SetWindowsHookEx(WH_MOUSE_LL, proc,
                    GetModuleHandle(curModule.ModuleName), 0);
            }
        }

        private IntPtr MouseHookCallback(int nCode, IntPtr wParam, IntPtr lParam)
        {
            if (nCode >= 0 && wParam == (IntPtr)WM_MOUSEMOVE && pendingMovements.Count > 0)
            {
                // Inject our recoil compensation into the mouse movement stream
                var movement = pendingMovements.Dequeue();

                // Modify the mouse data structure directly
                var mouseStruct = Marshal.PtrToStructure<MSLLHOOKSTRUCT>(lParam);
                mouseStruct.pt.X += movement.X;
                mouseStruct.pt.Y += movement.Y;
                Marshal.StructureToPtr(mouseStruct, lParam, false);

                Console.WriteLine($"Hardware-level injection: {movement.X}, {movement.Y}");
            }

            return CallNextHookEx(mouseHookID, nCode, wParam, lParam);
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct MSLLHOOKSTRUCT
        {
            public POINT pt;
            public uint mouseData;
            public uint flags;
            public uint time;
            public IntPtr dwExtraInfo;
        }
        
        private void StartRecoilMonitoring()
        {
            if (!recoilTimer.IsEnabled)
            {
                recoilTimer.Start();
            }
        }
        
        private void StopRecoilMonitoring()
        {
            recoilTimer.Stop();
            isRecoilActive = false;
        }
        
        private void RecoilTimer_Tick(object sender, EventArgs e)
        {
            if (MouseRecoilToggle.IsChecked != true) return;

            bool isActivationKeyPressed = GetActivationKeyPressed();

            if (isActivationKeyPressed && !isRecoilActive)
            {
                isRecoilActive = true;
                recoilStartTime = DateTime.Now;
                Console.WriteLine("Recoil compensation STARTED");
            }
            else if (!isActivationKeyPressed && isRecoilActive)
            {
                isRecoilActive = false;
                Console.WriteLine("Recoil compensation STOPPED");
            }

            if (isRecoilActive)
            {
                ApplyRecoilCompensation();
            }
        }
        
        private bool GetActivationKeyPressed()
        {
            string selectedKey = ((ComboBoxItem)ActivationKeyCombo.SelectedItem)?.Content?.ToString();
            
            return selectedKey switch
            {
                "LMouse" => (GetAsyncKeyState(VK_LBUTTON) & 0x8000) != 0,
                "RMouse" => (GetAsyncKeyState(VK_RBUTTON) & 0x8000) != 0,
                _ => false
            };
        }
        
        private void ApplyRecoilCompensation()
        {
            double delayMs = DelaySlider.Value;
            TimeSpan elapsed = DateTime.Now - recoilStartTime;

            if (elapsed.TotalMilliseconds < delayMs)
                return;

            double horizontalValue = HorizontalSlider.Value;
            double verticalValue = VerticalSlider.Value;
            double sensitivity = SensitivitySlider.Value;

            // Improved recoil compensation with proper scaling
            // Scale values based on elapsed time for progressive recoil
            double timeMultiplier = Math.Min(elapsed.TotalSeconds * 0.5, 1.0);

            // Use sensitivity multiplier for user control over strength
            int deltaX = (int)(horizontalValue * 1.2 * timeMultiplier * sensitivity);
            int deltaY = (int)(verticalValue * 1.2 * timeMultiplier * sensitivity);

            if (deltaX != 0 || deltaY != 0)
            {
                // Debug output to verify recoil is being applied
                Console.WriteLine($"Applying recoil: deltaX={deltaX}, deltaY={deltaY}, elapsed={elapsed.TotalMilliseconds}ms");

                // Use SendInput for better game compatibility
                SendMouseInput(deltaX, deltaY);
            }
        }

        private void SendMouseInput(int deltaX, int deltaY)
        {
            // Add small random variations to avoid detection
            double randomFactor = 1.0 + (random.NextDouble() - 0.5) * 0.1; // ±5% variation
            deltaX = (int)(deltaX * randomFactor);
            deltaY = (int)(deltaY * randomFactor);

            Console.WriteLine($"Attempting mouse input: deltaX={deltaX}, deltaY={deltaY}, useHardware={useHardwareLevel}");

            // Try multiple methods for maximum compatibility
            bool success = false;

            // Method 1: Physical USB mouse emulation (most undetectable)
            if (!success)
            {
                success = TryPhysicalMouseEmulation(deltaX, deltaY);
            }

            // Method 2: Kernel-level device injection
            if (useKernelLevel && !success)
            {
                success = TryKernelLevelInjection(deltaX, deltaY);
            }

            // Method 3: Hardware-level injection
            if (useHardwareLevel && !success)
            {
                success = TryHardwareLevelInjection(deltaX, deltaY);
            }

            // Method 4: Raw cursor position manipulation
            if (!success)
            {
                success = TryRawCursorManipulation(deltaX, deltaY);
            }

            // Method 3: SendInput with hardware simulation
            if (!success)
            {
                success = TrySendInputHardware(deltaX, deltaY);
            }

            // Method 4: Direct cursor manipulation
            if (!success)
            {
                success = TryDirectCursorMove(deltaX, deltaY);
            }

            // Method 5: Multiple small movements
            if (!success)
            {
                TryMultipleSmallMovements(deltaX, deltaY);
            }

            Console.WriteLine($"Mouse input result: success={success}");
        }

        private bool TryPhysicalMouseEmulation(int deltaX, int deltaY)
        {
            try
            {
                // This method simulates physical mouse movement by directly manipulating
                // the cursor position in a way that appears as natural mouse movement

                POINT currentPos;
                if (!GetCursorPos(out currentPos))
                    return false;

                // Calculate target position
                int targetX = currentPos.X + deltaX;
                int targetY = currentPos.Y + deltaY;

                // Simulate natural mouse movement with multiple small steps
                int steps = Math.Max(1, Math.Max(Math.Abs(deltaX), Math.Abs(deltaY)) / 2);
                steps = Math.Min(steps, 10); // Limit steps for performance

                for (int i = 1; i <= steps; i++)
                {
                    // Calculate intermediate position with slight randomization
                    double progress = (double)i / steps;
                    int stepX = currentPos.X + (int)(deltaX * progress);
                    int stepY = currentPos.Y + (int)(deltaY * progress);

                    // Add micro-jitter to simulate human movement
                    stepX += random.Next(-1, 2);
                    stepY += random.Next(-1, 2);

                    // Set cursor position
                    SetCursorPos(stepX, stepY);

                    // Small delay to simulate natural movement speed
                    if (i < steps)
                    {
                        System.Threading.Thread.Sleep(random.Next(1, 3));
                    }
                }

                // Verify final position
                POINT finalPos;
                if (GetCursorPos(out finalPos))
                {
                    int actualDeltaX = finalPos.X - currentPos.X;
                    int actualDeltaY = finalPos.Y - currentPos.Y;

                    bool success = Math.Abs(actualDeltaX - deltaX) <= 3 && Math.Abs(actualDeltaY - deltaY) <= 3;
                    if (success)
                    {
                        Console.WriteLine($"Physical mouse emulation successful: expected({deltaX},{deltaY}) actual({actualDeltaX},{actualDeltaY})");
                    }
                    return success;
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Physical mouse emulation failed: {ex.Message}");
                return false;
            }
        }

        private bool TryKernelLevelInjection(int deltaX, int deltaY)
        {
            try
            {
                if (deviceHandle == IntPtr.Zero || deviceHandle.ToInt32() == -1)
                    return false;

                // Create HID mouse report structure
                byte[] mouseReport = new byte[4];
                mouseReport[0] = 0x01; // Report ID
                mouseReport[1] = 0x00; // Button state
                mouseReport[2] = (byte)(deltaX & 0xFF); // X movement (low byte)
                mouseReport[3] = (byte)(deltaY & 0xFF); // Y movement (low byte)

                // Send the report directly to the device
                IntPtr buffer = Marshal.AllocHGlobal(mouseReport.Length);
                Marshal.Copy(mouseReport, 0, buffer, mouseReport.Length);

                uint bytesReturned;
                bool result = DeviceIoControl(deviceHandle, IOCTL_HID_SET_FEATURE, buffer,
                    (uint)mouseReport.Length, IntPtr.Zero, 0, out bytesReturned, IntPtr.Zero);

                Marshal.FreeHGlobal(buffer);

                if (result)
                {
                    Console.WriteLine($"Kernel-level injection successful: {deltaX}, {deltaY}");
                    return true;
                }
                else
                {
                    Console.WriteLine($"Kernel-level injection failed: {Marshal.GetLastWin32Error()}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Kernel-level injection exception: {ex.Message}");
                return false;
            }
        }

        private bool TryHardwareLevelInjection(int deltaX, int deltaY)
        {
            try
            {
                if (mouseHookID != IntPtr.Zero)
                {
                    pendingMovements.Enqueue(new POINT { X = deltaX, Y = deltaY });

                    // Trigger a small mouse movement to activate the hook
                    POINT currentPos;
                    if (GetCursorPos(out currentPos))
                    {
                        SetCursorPos(currentPos.X, currentPos.Y);
                        return true;
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private bool TryRawCursorManipulation(int deltaX, int deltaY)
        {
            try
            {
                POINT currentPos;
                if (GetCursorPos(out currentPos))
                {
                    // Apply movement directly to cursor position
                    bool result = SetCursorPos(currentPos.X + deltaX, currentPos.Y + deltaY);

                    // Verify the movement was applied
                    POINT newPos;
                    if (GetCursorPos(out newPos))
                    {
                        int actualDeltaX = newPos.X - currentPos.X;
                        int actualDeltaY = newPos.Y - currentPos.Y;
                        Console.WriteLine($"Raw cursor move: expected({deltaX},{deltaY}) actual({actualDeltaX},{actualDeltaY})");
                        return Math.Abs(actualDeltaX - deltaX) <= 2 && Math.Abs(actualDeltaY - deltaY) <= 2;
                    }

                    return result;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private bool TrySendInputHardware(int deltaX, int deltaY)
        {
            try
            {
                INPUT[] inputs = new INPUT[1];
                inputs[0].type = INPUT_MOUSE;
                inputs[0].u.mi.dx = deltaX;
                inputs[0].u.mi.dy = deltaY;
                inputs[0].u.mi.dwFlags = MOUSEEVENTF_MOVE;
                inputs[0].u.mi.time = 0;
                inputs[0].u.mi.dwExtraInfo = GetMessageExtraInfo();

                uint result = SendInput(1, inputs, Marshal.SizeOf(typeof(INPUT)));
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        private bool TryDirectCursorMove(int deltaX, int deltaY)
        {
            try
            {
                POINT currentPos;
                if (GetCursorPos(out currentPos))
                {
                    return SetCursorPos(currentPos.X + deltaX, currentPos.Y + deltaY);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private void TryMultipleSmallMovements(int deltaX, int deltaY)
        {
            // Split movement into smaller, more natural chunks
            int steps = Math.Max(1, Math.Abs(deltaX) / 2 + Math.Abs(deltaY) / 2);
            steps = Math.Min(steps, 5); // Limit to 5 steps max

            for (int i = 0; i < steps; i++)
            {
                int stepX = deltaX / steps;
                int stepY = deltaY / steps;

                // Add micro-variations to each step
                stepX += random.Next(-1, 2);
                stepY += random.Next(-1, 2);

                INPUT[] inputs = new INPUT[1];
                inputs[0].type = INPUT_MOUSE;
                inputs[0].u.mi.dx = stepX;
                inputs[0].u.mi.dy = stepY;
                inputs[0].u.mi.dwFlags = MOUSEEVENTF_MOVE;
                inputs[0].u.mi.time = 0;
                inputs[0].u.mi.dwExtraInfo = IntPtr.Zero;

                SendInput(1, inputs, Marshal.SizeOf(typeof(INPUT)));

                // Small delay between movements for naturalness
                if (i < steps - 1)
                {
                    Task.Delay(random.Next(1, 3)).Wait();
                }
            }
        }



        // Cleanup method
        public void Cleanup()
        {
            try
            {
                if (mouseHookID != IntPtr.Zero)
                {
                    UnhookWindowsHookEx(mouseHookID);
                    mouseHookID = IntPtr.Zero;
                    Console.WriteLine("Hardware-level mouse hook cleaned up");
                }

                if (deviceHandle != IntPtr.Zero && deviceHandle.ToInt32() != -1)
                {
                    CloseHandle(deviceHandle);
                    deviceHandle = IntPtr.Zero;
                    Console.WriteLine("Kernel-level device handle cleaned up");
                }

                recoilTimer?.Stop();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during cleanup: {ex.Message}");
            }
        }

        // Destructor to ensure cleanup
        ~AntiRecoilWidget()
        {
            Cleanup();
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct POINT
        {
            public int X;
            public int Y;
        }
    }
} 