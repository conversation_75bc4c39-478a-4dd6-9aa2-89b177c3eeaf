using System;
using System.Runtime.InteropServices;
using System.Windows.Controls;
using System.Windows.Threading;

namespace FennecClient.UI.Controls
{
    public partial class AntiRecoilWidget : UserControl
    {
        // Native C++ RecoilEngine API
        [DllImport("FennecClient.Native.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern bool InitializeRecoilEngine();

        [DllImport("FennecClient.Native.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern void ShutdownRecoilEngine();

        [DllImport("FennecClient.Native.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern bool SetRecoilConfig(ref RecoilConfig config);

        [DllImport("FennecClient.Native.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern bool GetRecoilConfig(out RecoilConfig config);

        [DllImport("FennecClient.Native.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern bool StartRecoilCompensation();

        [DllImport("FennecClient.Native.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern bool StopRecoilCompensation();

        [DllImport("FennecClient.Native.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern bool IsRecoilActive();

        [DllImport("FennecClient.Native.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern bool GetRecoilStats(out RecoilStats stats);

        [DllImport("FennecClient.Native.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern void ResetRecoilStats();

        [DllImport("FennecClient.Native.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern bool IsInSupportedGame();

        // Recoil configuration structure (must match C++ struct)
        [StructLayout(LayoutKind.Sequential)]
        public struct RecoilConfig
        {
            public double horizontalStrength;
            public double verticalStrength;
            public double sensitivity;
            public double delayMs;
            public int activationKey; // VK_LBUTTON, VK_RBUTTON, etc.
            public bool enabled;
            public bool useRandomization;
            public double randomizationFactor; // 0.0 to 1.0
            public double smoothness; // 0.0 to 1.0
        }

        // Recoil statistics structure (must match C++ struct)
        [StructLayout(LayoutKind.Sequential)]
        public struct RecoilStats
        {
            public ulong totalCompensations;
            public ulong totalMouseMovements;
            public double averageLatencyMs;
            public double maxLatencyMs;
            public ulong missedFrames;
            public bool inGameWindow;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
            public string currentGame;
        }

        // Constants for activation keys
        private const int VK_LBUTTON = 0x01;
        private const int VK_RBUTTON = 0x02;

        // UI update timer
        private DispatcherTimer statusTimer;
        private bool isEngineInitialized = false;

        public AntiRecoilWidget()
        {
            InitializeComponent();
            InitializeRecoilSystem();
        }

        private void InitializeRecoilSystem()
        {
            try
            {
                // Initialize the native C++ recoil engine
                isEngineInitialized = InitializeRecoilEngine();

                if (!isEngineInitialized)
                {
                    Console.WriteLine("[RECOIL] Failed to initialize native recoil engine");
                    return;
                }

                Console.WriteLine("[RECOIL] Native recoil engine initialized successfully");

                // Set up UI event handlers
                MouseRecoilToggle.Checked += (s, e) => StartRecoilMonitoring();
                MouseRecoilToggle.Unchecked += (s, e) => StopRecoilMonitoring();

                // Set up status update timer
                statusTimer = new DispatcherTimer();
                statusTimer.Interval = TimeSpan.FromMilliseconds(100); // Update UI every 100ms
                statusTimer.Tick += StatusTimer_Tick;
                statusTimer.Start();

                // Initialize UI controls with default values
                InitializeUIControls();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RECOIL] Error initializing recoil system: {ex.Message}");
                isEngineInitialized = false;
            }
        }

        private void InitializeUIControls()
        {
            // Set default values for UI controls
            HorizontalSlider.Value = 0;
            VerticalSlider.Value = 0;
            SensitivitySlider.Value = 1.0;
            DelaySlider.Value = 0;

            // Set default activation key
            ActivationKeyCombo.SelectedIndex = 0; // LMouse

            // Update the native engine with default config
            UpdateNativeConfig();
        }

        private void StartRecoilMonitoring()
        {
            if (!isEngineInitialized)
            {
                Console.WriteLine("[RECOIL] Engine not initialized, cannot start recoil monitoring");
                return;
            }

            try
            {
                // Update configuration before starting
                UpdateNativeConfig();

                // Start the native recoil compensation
                bool success = StartRecoilCompensation();

                if (success)
                {
                    Console.WriteLine("[RECOIL] Recoil compensation started");
                }
                else
                {
                    Console.WriteLine("[RECOIL] Failed to start recoil compensation");
                    MouseRecoilToggle.IsChecked = false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RECOIL] Error starting recoil monitoring: {ex.Message}");
                MouseRecoilToggle.IsChecked = false;
            }
        }

        private void StopRecoilMonitoring()
        {
            if (!isEngineInitialized)
            {
                return;
            }

            try
            {
                bool success = StopRecoilCompensation();

                if (success)
                {
                    Console.WriteLine("[RECOIL] Recoil compensation stopped");
                }
                else
                {
                    Console.WriteLine("[RECOIL] Failed to stop recoil compensation");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RECOIL] Error stopping recoil monitoring: {ex.Message}");
            }
        }

        private void UpdateNativeConfig()
        {
            if (!isEngineInitialized)
            {
                return;
            }

            try
            {
                var config = new RecoilConfig
                {
                    horizontalStrength = HorizontalSlider.Value,
                    verticalStrength = VerticalSlider.Value,
                    sensitivity = SensitivitySlider.Value,
                    delayMs = DelaySlider.Value,
                    activationKey = GetSelectedActivationKey(),
                    enabled = MouseRecoilToggle.IsChecked == true,
                    useRandomization = true,
                    randomizationFactor = 0.2, // Fixed value since no UI slider
                    smoothness = 0.5 // Fixed value since no UI slider
                };

                bool success = SetRecoilConfig(ref config);

                if (!success)
                {
                    Console.WriteLine("[RECOIL] Failed to update native config");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RECOIL] Error updating native config: {ex.Message}");
            }
        }

        private int GetSelectedActivationKey()
        {
            string selectedKey = ((ComboBoxItem)ActivationKeyCombo.SelectedItem)?.Content?.ToString();

            return selectedKey switch
            {
                "LMouse" => VK_LBUTTON,
                "RMouse" => VK_RBUTTON,
                _ => VK_LBUTTON
            };
        }

        private void StatusTimer_Tick(object sender, EventArgs e)
        {
            if (!isEngineInitialized)
            {
                return;
            }

            try
            {
                // Update UI with current status
                bool isActive = IsRecoilActive();
                bool inGame = IsInSupportedGame();

                // Update status indicators (if you have them in XAML)
                // You can add status labels to show current state

                // Get and display statistics
                if (GetRecoilStats(out RecoilStats stats))
                {
                    // Update statistics display if needed
                    // Console.WriteLine($"[RECOIL] Stats - Compensations: {stats.totalCompensations}, In Game: {stats.inGameWindow}, Current Game: {stats.currentGame}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RECOIL] Error in status update: {ex.Message}");
            }
        }

        // Cleanup method
        public void Cleanup()
        {
            try
            {
                statusTimer?.Stop();

                if (isEngineInitialized)
                {
                    StopRecoilCompensation();
                    ShutdownRecoilEngine();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RECOIL] Error during cleanup: {ex.Message}");
            }
        }

        // Destructor to ensure cleanup
        ~AntiRecoilWidget()
        {
            Cleanup();
        }
    }
}