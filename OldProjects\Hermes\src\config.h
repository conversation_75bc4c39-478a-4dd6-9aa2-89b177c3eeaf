#pragma once

#include <string>
#include <vector>

// Ensure the config directory exists
bool EnsureConfigDirectoryExists();

// Get list of all config files
std::vector<std::string> GetConfigFiles();

// Save current settings to a config file
bool SaveConfig(const std::string& configName);

// Delete a config file
bool DeleteConfig(const std::string& configName);

// Load settings from a config file
bool LoadConfig(const std::string& configName); 