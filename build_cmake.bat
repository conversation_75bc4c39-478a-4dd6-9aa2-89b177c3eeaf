@echo off
echo Building FennecClient.Native with CMake...

REM Check if CMake is available
where cmake >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo CMake not found. Please install CMake and add it to PATH.
    echo You can download it from: https://cmake.org/download/
    pause
    exit /b 1
)

REM Create build directory
if not exist "FennecClient.Native\build" mkdir "FennecClient.Native\build"

REM Navigate to build directory
cd FennecClient.Native\build

REM Generate build files
echo Generating build files...
cmake .. -G "Visual Studio 17 2022" -A x64

if %ERRORLEVEL% NEQ 0 (
    echo CMake generation failed! Trying with MinGW...
    cmake .. -G "MinGW Makefiles"
    
    if %ERRORLEVEL% NEQ 0 (
        echo CMake generation failed with both generators!
        cd ..\..
        pause
        exit /b 1
    )
    
    echo Building with MinGW...
    mingw32-make
    
    if %ERRORLEVEL% NEQ 0 (
        echo MinGW build failed!
        cd ..\..
        pause
        exit /b 1
    )
) else (
    REM Build with Visual Studio
    echo Building Debug configuration...
    cmake --build . --config Debug
    
    if %ERRORLEVEL% NEQ 0 (
        echo Debug build failed!
        cd ..\..
        pause
        exit /b 1
    )
    
    echo Building Release configuration...
    cmake --build . --config Release
    
    if %ERRORLEVEL% NEQ 0 (
        echo Release build failed!
        cd ..\..
        pause
        exit /b 1
    )
)

cd ..\..

echo Native build completed successfully!
echo.
echo DLL files should be in:
echo - FennecClient.App\bin\Debug\net8.0-windows\FennecClient.Native.dll
echo - FennecClient.App\bin\Release\net8.0-windows\FennecClient.Native.dll
echo.
pause
