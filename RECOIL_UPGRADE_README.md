# Fennec Recoil System Upgrade

## Übersicht

Das Recoil-System wurde von C# nach C++ migriert, um bessere Performance und Zuverlässigkeit zu erreichen. Die neue Architektur basiert auf der bewährten Hermes-Implementierung.

## Architektur

### V<PERSON>her (C# Problem)
- **Performance-Probleme**: DispatcherTimer mit 2ms Intervall war ineffizient
- **Komplexe Multi-Method-Ansätze**: Hardware-Level, Kernel-Level, Memory-Patching gleichzeitig
- **Fehlende Game-Detection**: Funktionierte überall, nicht nur in Spielen
- **Überkomplizierte Eingabe-Methoden**: Zu viele verschiedene Injection-Methoden

### Nachher (C++ Lösung)
- **Effiziente C++ Engine**: Basiert auf bewährter Hermes-Implementierung
- **Einfache, effektive Maus-Eingabe**: Nur `SendInput` mit `MOUSEEVENTF_MOVE`
- **Präzise Game-Detection**: Prüft aktive Fenster und unterstützte Spiele
- **Optimierte Timing-Logik**: 3ms Minimum-Intervall mit intelligenter Übersprunglogik
- **Saubere UI-Trennung**: C# nur für UI, C++ für alle Recoil-Logik

## Dateien

### C++ Native Engine
- `FennecClient.Native/RecoilEngine.h` - Header mit API-Definitionen
- `FennecClient.Native/RecoilEngine.cpp` - Hauptimplementierung
- `FennecClient.Native/pch.h` - Precompiled Headers
- `FennecClient.Native/pch.cpp` - PCH Source
- `FennecClient.Native/FennecClient.Native.vcxproj` - Visual Studio Projekt
- `FennecClient.Native/CMakeLists.txt` - CMake Build-System

### C# UI Integration
- `FennecClient.UI/Controls/AntiRecoilWidget.xaml.cs` - Vereinfachte UI-Klasse

### Build-Skripte
- `build_native.bat` - MSBuild-basiertes Build-Skript
- `build_cmake.bat` - CMake-basiertes Build-Skript

## Unterstützte Spiele

Die Engine unterstützt automatisch folgende Spiele:
- Apex Legends (r5apex.exe, r5apex_dx12.exe)
- Escape from Tarkov (escapefromtarkov.exe)
- Fortnite (fortniteclient-win64-shipping.exe, fortniteclient-win64-shipping_eac_eos.exe)
- Modern Warfare 2019 (modernwarfare.exe)
- PUBG (tslgame.exe)
- Rainbow Six Siege (rainbowsix.exe, rainbowsix_be.exe, rainbowsix_vulkan.exe)
- Rust (rustclient.exe)
- Black Ops 6 (cod.exe)
- Modern Warfare II (cod22-cod.exe)
- Modern Warfare III (cod23-cod.exe)
- Counter-Strike 2 (cs2.exe)
- Delta Force (deltaforceclient-win64-shipping.exe)

## Build-Anweisungen

### Option 1: Visual Studio
1. Öffne `FennecClient.sln` in Visual Studio
2. Stelle sicher, dass das `FennecClient.Native` Projekt in der Solution ist
3. Baue die Solution (Build -> Build Solution)

### Option 2: MSBuild (Command Line)
```cmd
# Aus Visual Studio Developer Command Prompt
msbuild FennecClient.Native\FennecClient.Native.vcxproj /p:Configuration=Release /p:Platform=x64
```

### Option 3: CMake
```cmd
cd FennecClient.Native
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

## API-Übersicht

### C++ API (extern "C")
```cpp
// Engine Management
bool InitializeRecoilEngine();
void ShutdownRecoilEngine();

// Configuration
bool SetRecoilConfig(const RecoilConfig* config);
bool GetRecoilConfig(RecoilConfig* config);

// Control
bool StartRecoilCompensation();
bool StopRecoilCompensation();
bool IsRecoilActive();

// Statistics
bool GetRecoilStats(RecoilStats* stats);
void ResetRecoilStats();

// Game Detection
bool IsInSupportedGame();
```

### C# Integration
```csharp
// P/Invoke Deklarationen für alle C++ Funktionen
[DllImport("FennecClient.Native.dll", CallingConvention = CallingConvention.Cdecl)]
private static extern bool InitializeRecoilEngine();

// Strukturen müssen exakt mit C++ übereinstimmen
[StructLayout(LayoutKind.Sequential)]
public struct RecoilConfig { ... }
```

## Konfiguration

### RecoilConfig Struktur
```cpp
struct RecoilConfig {
    double horizontalStrength;    // Horizontale Recoil-Stärke
    double verticalStrength;      // Vertikale Recoil-Stärke
    double sensitivity;           // Maus-Sensitivität
    double delayMs;              // Verzögerung vor Recoil-Start
    int activationKey;           // VK_LBUTTON, VK_RBUTTON, etc.
    bool enabled;                // Engine aktiviert
    bool useRandomization;       // Randomisierung verwenden
    double randomizationFactor;  // Randomisierungs-Faktor (0.0-1.0)
    double smoothness;           // Glättung zwischen Recoil-Schritten (0.0-1.0)
};
```

## Performance-Verbesserungen

### Timing
- **Vorher**: C# DispatcherTimer mit 2ms (ineffizient)
- **Nachher**: C++ Thread mit 1ms Präzision und intelligenter Übersprunglogik

### Game Detection
- **Vorher**: Keine Game-Detection
- **Nachher**: Effiziente Fenster- und Prozess-Erkennung (nur alle 100ms)

### Mouse Input
- **Vorher**: Komplexe Multi-Method-Ansätze
- **Nachher**: Einfaches, bewährtes `SendInput` mit `MOUSEEVENTF_MOVE`

### Memory Usage
- **Vorher**: Hoher C# Overhead
- **Nachher**: Minimaler C++ Footprint

## Debugging

### Console Output
Die Engine gibt Debug-Informationen über `Console.WriteLine` und `OutputDebugString` aus:
```
[RECOIL] Native recoil engine initialized successfully
[RECOIL] Recoil compensation started
[RECOIL] Active - Step: 1, X: 0.20, Y: 1.50, TimeSinceStart: 15ms
```

### Statistics
```cpp
struct RecoilStats {
    uint64_t totalCompensations;    // Anzahl Recoil-Kompensationen
    uint64_t totalMouseMovements;   // Anzahl Maus-Bewegungen
    double averageLatencyMs;        // Durchschnittliche Latenz
    double maxLatencyMs;           // Maximale Latenz
    uint64_t missedFrames;         // Verpasste Frames
    bool inGameWindow;             // Aktuell in unterstütztem Spiel
    string currentGame;            // Name des aktuellen Spiels
};
```

## Troubleshooting

### DLL nicht gefunden
- Stelle sicher, dass `FennecClient.Native.dll` im App-Verzeichnis ist
- Prüfe, ob alle Dependencies (Visual C++ Redistributable) installiert sind

### Recoil funktioniert nicht
- Prüfe Console-Output für Fehlermeldungen
- Stelle sicher, dass ein unterstütztes Spiel läuft
- Prüfe, ob die Konfiguration korrekt ist

### Performance-Probleme
- Die neue C++ Engine sollte deutlich weniger CPU verwenden
- Prüfe Task Manager für CPU-Usage der Anwendung

## Migration von alter C# Implementierung

1. **Entferne alte C# Recoil-Logik** aus `AntiRecoilWidget.xaml.cs`
2. **Baue C++ Native Engine** mit einem der Build-Skripte
3. **Teste die neue Implementierung** in unterstützten Spielen
4. **Konfiguriere UI-Controls** für neue Parameter (Smoothness, etc.)

Die neue Implementierung sollte deutlich zuverlässiger und performanter sein!
