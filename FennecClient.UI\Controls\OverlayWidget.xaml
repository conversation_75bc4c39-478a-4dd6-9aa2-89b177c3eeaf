<UserControl x:Class="FennecClient.UI.Controls.OverlayWidget"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="OverlayConverter"/>
    </UserControl.Resources>
    
    <Border Background="#222328" CornerRadius="5" Padding="12" Margin="4">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="8" BlurRadius="25" Opacity="0.3"/>
        </Border.Effect>
        <StackPanel>
            <Grid Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Border Grid.Column="0" Background="#fa0000" Width="40" Height="40" CornerRadius="20" Margin="0,0,12,0">
                    <TextBlock Text="&#xE8AB;" FontFamily="Segoe MDL2 Assets" FontSize="18" 
                               Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="Overlay" FontSize="18" FontWeight="SemiBold" Foreground="#e4e5e6"/>
                    <TextBlock Text="Game overlay enhancements" FontSize="14" Foreground="#888888" FontStyle="Italic"/>
                </StackPanel>
                <ToggleButton Grid.Column="1" x:Name="OverlayToggle" HorizontalAlignment="Right" VerticalAlignment="Center"
                              Width="50" Height="25" Background="#333338" BorderThickness="0">
                    <ToggleButton.Style>
                        <Style TargetType="ToggleButton">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="ToggleButton">
                                        <Border x:Name="Border" Background="#333338" CornerRadius="12" BorderThickness="1" BorderBrush="#555555">
                                            <Ellipse x:Name="Thumb" Width="18" Height="18" Fill="#888888" HorizontalAlignment="Left" Margin="3,0,0,0"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsChecked" Value="True">
                                                <Setter TargetName="Border" Property="Background" Value="#fa0000"/>
                                                <Setter TargetName="Border" Property="BorderBrush" Value="#fa0000"/>
                                                <Setter TargetName="Thumb" Property="Fill" Value="White"/>
                                                <Setter TargetName="Thumb" Property="HorizontalAlignment" Value="Right"/>
                                                <Setter TargetName="Thumb" Property="Margin" Value="0,0,3,0"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </ToggleButton.Style>
                </ToggleButton>
            </Grid>
            
            <!-- Divider -->
            <Border Background="#333338" Height="1" Margin="0,12,0,0"/>
            
            <StackPanel x:Name="ContentArea" Margin="0,12,0,0">
                <TextBlock Text="HUD overlay functionality has been moved to the Crosshair tab for better organization." 
                           FontSize="14" Foreground="#888888" FontStyle="Italic" TextAlignment="Center" Margin="0,20,0,0"/>
            </StackPanel>
        </StackPanel>
    </Border>
</UserControl> 