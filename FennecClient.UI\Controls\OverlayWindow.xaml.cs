using System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Forms;
using System.Windows.Threading;

namespace FennecClient.UI.Controls
{
    public partial class OverlayWindow : Window
    {

        public OverlayWindow()
        {
            InitializeComponent();
            InitializeOverlay();
        }

        private void InitializeOverlay()
        {
            // Detect primary monitor and set window bounds
            var primaryScreen = Screen.PrimaryScreen;
            this.Left = primaryScreen.Bounds.Left;
            this.Top = primaryScreen.Bounds.Top;
            this.Width = primaryScreen.Bounds.Width;
            this.Height = primaryScreen.Bounds.Height;

            // Make window click-through
            this.IsHitTestVisible = false;
            
            // Debug output
            Console.WriteLine($"Primary screen bounds: {primaryScreen.Bounds}");
            Console.WriteLine($"Overlay window size: {this.Width}x{this.Height} at {this.Left},{this.Top}");
            
            // Hide initially
            this.Visibility = Visibility.Hidden;
        }



        public void ShowOverlay()
        {
            this.Visibility = Visibility.Visible;
        }

        public void HideOverlay()
        {
            this.Visibility = Visibility.Hidden;
        }

        public void UpdateCrosshair(bool isVisible, double size, double thickness, double opacity)
        {
            if (isVisible)
            {
                CrosshairHorizontal.Width = size;
                CrosshairHorizontal.Height = thickness;
                CrosshairHorizontal.Opacity = opacity / 100.0;
                CrosshairHorizontal.Visibility = Visibility.Visible;

                CrosshairVertical.Width = thickness;
                CrosshairVertical.Height = size;
                CrosshairVertical.Opacity = opacity / 100.0;
                CrosshairVertical.Visibility = Visibility.Visible;

                // Update canvas positioning
                Canvas.SetLeft(CrosshairHorizontal, -size / 2);
                Canvas.SetTop(CrosshairHorizontal, -thickness / 2);
                Canvas.SetLeft(CrosshairVertical, -thickness / 2);
                Canvas.SetTop(CrosshairVertical, -size / 2);
            }
            else
            {
                CrosshairHorizontal.Visibility = Visibility.Collapsed;
                CrosshairVertical.Visibility = Visibility.Collapsed;
            }
        }



        protected override void OnClosed(EventArgs e)
        {
            base.OnClosed(e);
        }
    }
} 