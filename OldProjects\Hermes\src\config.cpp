#define _CRT_SECURE_NO_WARNINGS

#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <filesystem>
#include <Windows.h>
#include "config.h"

// External references to the variables we need to save/load
extern float g_AccentColor[3];
extern float g_TextColor[3];
extern float g_BackgroundColor[3];
extern float g_WidgetColor[3];
extern float g_BorderColor[3];
extern int g_MenuTransparency;
extern bool g_Streamproof;
// FPS counter and limiter removed for performance
extern bool g_AlwaysOnTop;
extern bool g_LockPosition;
extern bool g_EnableBackgroundAnimation;
extern int g_BackgroundAnimationTransparency;
// Add external references for key bindings
extern int g_ToggleUIKey;
extern int g_BlockInputKey;
extern int g_RecoilPauseKey;

// Create the config directory if it doesn't exist
bool EnsureConfigDirectoryExists() {
    try {
        std::filesystem::path dirPath = "C:\\Hermes";

        if (!std::filesystem::exists(dirPath)) {
            printf("Creating config directory at %s\n", dirPath.string().c_str());
            return std::filesystem::create_directory(dirPath);
        }
        return true;
    }
    catch (const std::exception& e) {
        printf("Error creating config directory: %s\n", e.what());
        return false;
    }
}

// Get list of all config files
std::vector<std::string> GetConfigFiles() {
    std::vector<std::string> files;

    try {
        std::filesystem::path dirPath = "C:\\Hermes";

        if (!std::filesystem::exists(dirPath)) {
            EnsureConfigDirectoryExists();
            return files;
        }

        for (const auto& entry : std::filesystem::directory_iterator(dirPath)) {
            if (entry.is_regular_file() && entry.path().extension() == ".cfg") {
                // Store just the filename without extension
                std::string filename = entry.path().filename().string();
                filename = filename.substr(0, filename.length() - 4); // Remove .cfg
                files.push_back(filename);
            }
        }
    }
    catch (const std::exception& e) {
        printf("Error reading config directory: %s\n", e.what());
    }

    return files;
}

// Save current settings to a config file
bool SaveConfig(const std::string& configName) {
    if (!EnsureConfigDirectoryExists()) {
        return false;
    }

    std::string filePath = "C:\\Hermes\\" + configName + ".cfg";
    std::ofstream configFile(filePath);

    if (!configFile.is_open()) {
        printf("Failed to open config file for writing: %s\n", filePath.c_str());
        return false;
    }

    // Write settings to the file
    configFile << "[Colors]\n";
    configFile << "AccentColor=" << g_AccentColor[0] << "," << g_AccentColor[1] << "," << g_AccentColor[2] << "\n";
    configFile << "TextColor=" << g_TextColor[0] << "," << g_TextColor[1] << "," << g_TextColor[2] << "\n";
    configFile << "BackgroundColor=" << g_BackgroundColor[0] << "," << g_BackgroundColor[1] << "," << g_BackgroundColor[2] << "\n";
    configFile << "WidgetColor=" << g_WidgetColor[0] << "," << g_WidgetColor[1] << "," << g_WidgetColor[2] << "\n";
    configFile << "BorderColor=" << g_BorderColor[0] << "," << g_BorderColor[1] << "," << g_BorderColor[2] << "\n";

    configFile << "\n[Interface]\n";
    configFile << "MenuTransparency=" << g_MenuTransparency << "\n";
    configFile << "Streamproof=" << (g_Streamproof ? "1" : "0") << "\n";
    configFile << "AlwaysOnTop=" << (g_AlwaysOnTop ? "1" : "0") << "\n";
    configFile << "LockPosition=" << (g_LockPosition ? "1" : "0") << "\n";
    configFile << "EnableBackgroundAnimation=" << (g_EnableBackgroundAnimation ? "1" : "0") << "\n";
    configFile << "BackgroundAnimationTransparency=" << g_BackgroundAnimationTransparency << "\n";

    // Add key bindings section
    configFile << "\n[KeyBindings]\n";
    configFile << "ToggleUIKey=" << g_ToggleUIKey << "\n";
    configFile << "BlockInputKey=" << g_BlockInputKey << "\n";
    configFile << "RecoilPauseKey=" << g_RecoilPauseKey << "\n";

    configFile.close();
    printf("Config saved successfully to %s\n", filePath.c_str());
    return true;
}

// Delete a config file
bool DeleteConfig(const std::string& configName) {
    std::string filePath = "C:\\Hermes\\" + configName + ".cfg";

    try {
        return std::filesystem::remove(filePath);
    }
    catch (const std::exception& e) {
        printf("Error deleting config file: %s\n", e.what());
        return false;
    }
}

// Parse a line like "key=value"
bool ParseConfigLine(const std::string& line, std::string& key, std::string& value) {
    size_t equalsPos = line.find('=');
    if (equalsPos != std::string::npos) {
        key = line.substr(0, equalsPos);
        value = line.substr(equalsPos + 1);
        return true;
    }
    return false;
}

// Parse "r,g,b" into a float array
void ParseColorValue(const std::string& value, float color[3]) {
    std::string r, g, b;
    size_t firstComma = value.find(',');
    if (firstComma != std::string::npos) {
        r = value.substr(0, firstComma);
        size_t secondComma = value.find(',', firstComma + 1);
        if (secondComma != std::string::npos) {
            g = value.substr(firstComma + 1, secondComma - firstComma - 1);
            b = value.substr(secondComma + 1);

            try {
                color[0] = std::stof(r);
                color[1] = std::stof(g);
                color[2] = std::stof(b);
            }
            catch (...) {
                printf("Error parsing color value: %s\n", value.c_str());
            }
        }
    }
}

// Load settings from a config file
bool LoadConfig(const std::string& configName) {
    std::string filePath = "C:\\Hermes\\" + configName + ".cfg";
    std::ifstream configFile(filePath);

    if (!configFile.is_open()) {
        printf("Failed to open config file for reading: %s\n", filePath.c_str());
        return false;
    }

    std::string line;
    std::string section;

    while (std::getline(configFile, line)) {
        // Skip empty lines and comments
        if (line.empty() || line[0] == '#' || line[0] == ';') {
            continue;
        }

        // Check for section headers [Section]
        if (line[0] == '[' && line[line.length() - 1] == ']') {
            section = line.substr(1, line.length() - 2);
            continue;
        }

        // Parse key-value pairs
        std::string key, value;
        if (ParseConfigLine(line, key, value)) {
            if (section == "Colors") {
                if (key == "AccentColor") {
                    ParseColorValue(value, g_AccentColor);
                }
                else if (key == "TextColor") {
                    ParseColorValue(value, g_TextColor);
                }
                else if (key == "BackgroundColor") {
                    ParseColorValue(value, g_BackgroundColor);
                }
                else if (key == "WidgetColor") {
                    ParseColorValue(value, g_WidgetColor);
                }
                else if (key == "BorderColor") {
                    ParseColorValue(value, g_BorderColor);
                }
            }
            else if (section == "Interface") {
                if (key == "MenuTransparency") {
                    try {
                        g_MenuTransparency = std::stoi(value);
                    }
                    catch (...) {
                        printf("Error parsing MenuTransparency value: %s\n", value.c_str());
                    }
                }
                else if (key == "Streamproof") {
                    g_Streamproof = (value == "1");
                }
                else if (key == "AlwaysOnTop") {
                    g_AlwaysOnTop = (value == "1");
                }
                else if (key == "LockPosition") {
                    g_LockPosition = (value == "1");
                }
                else if (key == "EnableBackgroundAnimation") {
                    g_EnableBackgroundAnimation = (value == "1");
                }
                else if (key == "BackgroundAnimationTransparency") {
                    try {
                        g_BackgroundAnimationTransparency = std::stoi(value);
                    }
                    catch (...) {
                        printf("Error parsing BackgroundAnimationTransparency value: %s\n", value.c_str());
                    }
                }
            }
            else if (section == "KeyBindings") {
                if (key == "ToggleUIKey") {
                    try {
                        g_ToggleUIKey = std::stoi(value);
                    }
                    catch (...) {
                        printf("Error parsing ToggleUIKey value: %s\n", value.c_str());
                    }
                }
                else if (key == "BlockInputKey") {
                    try {
                        g_BlockInputKey = std::stoi(value);
                    }
                    catch (...) {
                        printf("Error parsing BlockInputKey value: %s\n", value.c_str());
                    }
                }
                else if (key == "RecoilPauseKey") {
                    try {
                        g_RecoilPauseKey = std::stoi(value);
                    }
                    catch (...) {
                        printf("Error parsing RecoilPauseKey value: %s\n", value.c_str());
                    }
                }
            }
        }
    }

    configFile.close();
    printf("Config loaded successfully from %s\n", filePath.c_str());

    // Re-register global hotkeys with the new values
    #ifdef _WIN32
    extern HWND g_HiddenWindow;
    extern bool RegisterGlobalHotkey(int hotkeyId, int glfwKey);

    if (g_HiddenWindow) {
        RegisterGlobalHotkey(1, g_ToggleUIKey);  // 1 = HOTKEY_TOGGLEUI
        RegisterGlobalHotkey(2, g_RecoilPauseKey);  // 2 = HOTKEY_RECOILPAUSE
    }
    #endif

    return true;
}