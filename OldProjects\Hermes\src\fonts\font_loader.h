#pragma once

#include <vector>
#include <fstream>
#include <string>

namespace Launcher {
    // Lädt eine Font-Datei in einen Byte-Array
    static std::vector<unsigned char> LoadFontFromFile(const std::string& fontPath) {
        std::ifstream file(fontPath, std::ios::binary | std::ios::ate);
        if (!file.is_open()) {
            return {};
        }
        
        std::streamsize size = file.tellg();
        file.seekg(0, std::ios::beg);
        
        std::vector<unsigned char> buffer(size);
        if (file.read(reinterpret_cast<char*>(buffer.data()), size)) {
            return buffer;
        }
        
        return {};
    }
} 